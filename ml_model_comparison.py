#!/usr/bin/env python3
"""
Comprehensive ML Model Comparison: SVM vs Decision Trees vs Naive Bayes
Analyzes why <PERSON><PERSON>es performs exceptionally well for scam detection
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.feature_extraction.text import TfidfVectorizer, CountVectorizer
from sklearn.naive_bayes import MultinomialNB, GaussianNB, BernoulliNB
from sklearn.svm import SVC, LinearSVC
from sklearn.tree import DecisionTreeClassifier
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import (classification_report, confusion_matrix, accuracy_score, 
                           precision_score, recall_score, f1_score, roc_auc_score)
from sklearn.pipeline import Pipeline
import re
import time
from collections import Counter
import warnings
warnings.filterwarnings('ignore')

class MLModelComparison:
    def __init__(self, dataset_path):
        """Initialize the ML comparison framework"""
        self.dataset_path = dataset_path
        self.df = None
        self.X_train = None
        self.X_test = None
        self.y_train = None
        self.y_test = None
        self.results = {}
        
    def load_and_prepare_data(self):
        """Load and prepare the dataset"""
        print("="*70)
        print("LOADING AND PREPARING DATASET")
        print("="*70)
        
        self.df = pd.read_csv(self.dataset_path)
        print(f"Dataset loaded: {self.df.shape[0]} rows, {self.df.shape[1]} columns")
        
        # Create binary classification
        scam_labels = ['ADVANCE_FEE_SCAM', 'ROMANCE_SCAM', 'INVESTMENT_SCAM', 'SCAM_SAMPLE']
        self.df['is_scam'] = self.df['Label'].apply(lambda x: 1 if x in scam_labels else 0)
        
        print(f"Scam messages: {self.df['is_scam'].sum()}")
        print(f"Legitimate messages: {(self.df['is_scam'] == 0).sum()}")
        print(f"Class balance: {self.df['is_scam'].mean():.1%} scam")
        
        # Text preprocessing
        def clean_text(text):
            if pd.isna(text):
                return ""
            text = text.lower()
            text = re.sub(r'[^\w\s]', ' ', text)
            text = re.sub(r'\s+', ' ', text).strip()
            return text
        
        self.df['cleaned_text'] = self.df['Text Message / Email / Chat'].apply(clean_text)
        self.df = self.df[self.df['cleaned_text'].str.len() >= 10].reset_index(drop=True)
        
        print(f"After preprocessing: {len(self.df)} messages")
        
        # Split data
        X = self.df['cleaned_text']
        y = self.df['is_scam']
        
        self.X_train, self.X_test, self.y_train, self.y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        print(f"Training set: {len(self.X_train)} samples")
        print(f"Test set: {len(self.X_test)} samples")
        
    def analyze_text_characteristics(self):
        """Analyze text characteristics that favor Naive Bayes"""
        print("\n" + "="*70)
        print("TEXT CHARACTERISTICS ANALYSIS")
        print("="*70)
        
        # Feature sparsity analysis
        vectorizer = TfidfVectorizer(max_features=5000, stop_words='english')
        X_tfidf = vectorizer.fit_transform(self.df['cleaned_text'])
        
        sparsity = 1.0 - (X_tfidf.nnz / (X_tfidf.shape[0] * X_tfidf.shape[1]))
        print(f"Feature matrix sparsity: {sparsity:.1%}")
        print("High sparsity favors Naive Bayes due to its probabilistic nature")
        
        # Vocabulary analysis
        feature_names = vectorizer.get_feature_names_out()
        print(f"Vocabulary size: {len(feature_names)}")
        
        # Word frequency distribution
        scam_texts = ' '.join(self.df[self.df['is_scam'] == 1]['cleaned_text'])
        legit_texts = ' '.join(self.df[self.df['is_scam'] == 0]['cleaned_text'])
        
        def get_word_freq(text):
            words = re.findall(r'\b\w+\b', text.lower())
            return Counter(words)
        
        scam_words = get_word_freq(scam_texts)
        legit_words = get_word_freq(legit_texts)
        
        print(f"\nTop discriminative words in SCAM messages:")
        for word, count in scam_words.most_common(10):
            if len(word) > 3:
                print(f"  {word}: {count}")
        
        print(f"\nTop words in LEGITIMATE messages:")
        for word, count in legit_words.most_common(10):
            if len(word) > 3:
                print(f"  {word}: {count}")
                
    def train_and_evaluate_models(self):
        """Train and evaluate SVM, Decision Trees, and Naive Bayes models"""
        print("\n" + "="*70)
        print("MODEL TRAINING AND EVALUATION")
        print("="*70)
        
        # Define models with different configurations
        models = {
            # Naive Bayes variants
            'Naive Bayes (Multinomial)': Pipeline([
                ('tfidf', TfidfVectorizer(max_features=5000, stop_words='english', ngram_range=(1, 2))),
                ('nb', MultinomialNB(alpha=1.0))
            ]),
            'Naive Bayes (Bernoulli)': Pipeline([
                ('tfidf', TfidfVectorizer(max_features=5000, stop_words='english', ngram_range=(1, 2), binary=True)),
                ('nb', BernoulliNB(alpha=1.0))
            ]),
            
            # SVM variants
            'SVM (Linear)': Pipeline([
                ('tfidf', TfidfVectorizer(max_features=5000, stop_words='english', ngram_range=(1, 2))),
                ('svm', LinearSVC(C=1.0, random_state=42, max_iter=2000))
            ]),
            'SVM (RBF)': Pipeline([
                ('tfidf', TfidfVectorizer(max_features=3000, stop_words='english')),
                ('svm', SVC(C=1.0, kernel='rbf', random_state=42, probability=True))
            ]),
            
            # Decision Tree variants
            'Decision Tree (Basic)': Pipeline([
                ('tfidf', TfidfVectorizer(max_features=3000, stop_words='english')),
                ('dt', DecisionTreeClassifier(random_state=42, max_depth=10))
            ]),
            'Decision Tree (Optimized)': Pipeline([
                ('tfidf', TfidfVectorizer(max_features=3000, stop_words='english')),
                ('dt', DecisionTreeClassifier(random_state=42, max_depth=15, min_samples_split=5, min_samples_leaf=2))
            ]),
            'Random Forest': Pipeline([
                ('tfidf', TfidfVectorizer(max_features=3000, stop_words='english')),
                ('rf', RandomForestClassifier(n_estimators=100, random_state=42, max_depth=10))
            ])
        }
        
        # Train and evaluate each model
        for name, model in models.items():
            print(f"\nTraining {name}...")
            start_time = time.time()
            
            # Train model
            model.fit(self.X_train, self.y_train)
            training_time = time.time() - start_time
            
            # Make predictions
            start_time = time.time()
            y_pred = model.predict(self.X_test)
            prediction_time = time.time() - start_time
            
            # Calculate metrics
            accuracy = accuracy_score(self.y_test, y_pred)
            precision = precision_score(self.y_test, y_pred)
            recall = recall_score(self.y_test, y_pred)
            f1 = f1_score(self.y_test, y_pred)
            
            # Cross-validation
            cv_scores = cross_val_score(model, self.X_train, self.y_train, cv=5, scoring='accuracy')
            
            # Store results
            self.results[name] = {
                'model': model,
                'accuracy': accuracy,
                'precision': precision,
                'recall': recall,
                'f1_score': f1,
                'cv_mean': cv_scores.mean(),
                'cv_std': cv_scores.std(),
                'training_time': training_time,
                'prediction_time': prediction_time,
                'predictions': y_pred
            }
            
            print(f"  Accuracy: {accuracy:.4f}")
            print(f"  Precision: {precision:.4f}")
            print(f"  Recall: {recall:.4f}")
            print(f"  F1-Score: {f1:.4f}")
            print(f"  CV Score: {cv_scores.mean():.4f} (+/- {cv_scores.std() * 2:.4f})")
            print(f"  Training Time: {training_time:.3f}s")
            print(f"  Prediction Time: {prediction_time:.3f}s")
            
    def detailed_performance_analysis(self):
        """Detailed analysis of model performance"""
        print("\n" + "="*70)
        print("DETAILED PERFORMANCE ANALYSIS")
        print("="*70)
        
        # Create comparison DataFrame
        comparison_data = []
        for name, result in self.results.items():
            comparison_data.append({
                'Model': name,
                'Accuracy': result['accuracy'],
                'Precision': result['precision'],
                'Recall': result['recall'],
                'F1-Score': result['f1_score'],
                'CV Mean': result['cv_mean'],
                'CV Std': result['cv_std'],
                'Training Time (s)': result['training_time'],
                'Prediction Time (s)': result['prediction_time']
            })
        
        comparison_df = pd.DataFrame(comparison_data)
        comparison_df = comparison_df.sort_values('F1-Score', ascending=False)
        
        print("\nModel Performance Comparison:")
        print(comparison_df.round(4).to_string(index=False))
        
        # Best model analysis
        best_model_name = comparison_df.iloc[0]['Model']
        print(f"\nBest performing model: {best_model_name}")
        
        # Detailed classification report for best model
        best_predictions = self.results[best_model_name]['predictions']
        print(f"\nDetailed Classification Report for {best_model_name}:")
        print(classification_report(self.y_test, best_predictions, 
                                  target_names=['Legitimate', 'Scam']))
        
        # Confusion matrix
        print(f"\nConfusion Matrix for {best_model_name}:")
        cm = confusion_matrix(self.y_test, best_predictions)
        print(cm)
        
        return comparison_df
        
    def analyze_naive_bayes_advantages(self):
        """Analyze why Naive Bayes performs well for scam detection"""
        print("\n" + "="*70)
        print("WHY NAIVE BAYES EXCELS IN SCAM DETECTION")
        print("="*70)
        
        print("1. FEATURE INDEPENDENCE ASSUMPTION:")
        print("   - Text features (words) in scam detection are relatively independent")
        print("   - Presence of 'money', 'urgent', 'transfer' are independent indicators")
        print("   - This assumption works well for bag-of-words text classification")
        
        print("\n2. HIGH-DIMENSIONAL SPARSE DATA:")
        print("   - Text data creates high-dimensional feature spaces")
        print("   - Most features (words) are zero for any given document")
        print("   - Naive Bayes handles sparse data efficiently")
        
        print("\n3. SMALL DATASET PERFORMANCE:")
        print("   - Naive Bayes requires fewer training examples")
        print("   - Less prone to overfitting with limited data")
        print("   - Probabilistic approach provides good generalization")
        
        print("\n4. COMPUTATIONAL EFFICIENCY:")
        print("   - Linear time complexity for training and prediction")
        print("   - No iterative optimization required")
        print("   - Fast training and prediction times")
        
        print("\n5. PROBABILISTIC OUTPUT:")
        print("   - Provides probability estimates for predictions")
        print("   - Useful for confidence-based decision making")
        print("   - Can set custom thresholds for precision/recall trade-off")
        
        print("\n6. ROBUSTNESS TO IRRELEVANT FEATURES:")
        print("   - Performs well even with noisy or irrelevant features")
        print("   - Feature selection less critical than for other algorithms")
        print("   - Good baseline performance out-of-the-box")
        
        # Demonstrate with feature analysis
        nb_model = None
        for name, result in self.results.items():
            if 'Naive Bayes (Multinomial)' in name:
                nb_model = result['model']
                break
        
        if nb_model:
            print("\n7. FEATURE PROBABILITY ANALYSIS:")
            vectorizer = nb_model.named_steps['tfidf']
            classifier = nb_model.named_steps['nb']
            
            feature_names = vectorizer.get_feature_names_out()
            
            # Get log probabilities for each class
            log_prob_scam = classifier.feature_log_prob_[1]
            log_prob_legit = classifier.feature_log_prob_[0]
            
            # Calculate log probability ratios
            log_prob_ratio = log_prob_scam - log_prob_legit
            
            # Get top features for scam detection
            top_scam_indices = np.argsort(log_prob_ratio)[-10:]
            top_legit_indices = np.argsort(log_prob_ratio)[:10]
            
            print("   Top words indicating SCAM:")
            for idx in reversed(top_scam_indices):
                word = feature_names[idx]
                ratio = log_prob_ratio[idx]
                print(f"     {word}: {ratio:.3f}")
            
            print("   Top words indicating LEGITIMATE:")
            for idx in top_legit_indices:
                word = feature_names[idx]
                ratio = log_prob_ratio[idx]
                print(f"     {word}: {ratio:.3f}")

    def create_visualizations(self, comparison_df):
        """Create visualizations comparing model performance"""
        print("\n" + "="*70)
        print("CREATING PERFORMANCE VISUALIZATIONS")
        print("="*70)

        # Set up the plotting style
        plt.style.use('default')
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('ML Model Comparison for Scam Detection', fontsize=16, fontweight='bold')

        # 1. Accuracy Comparison
        ax1 = axes[0, 0]
        models = comparison_df['Model'].str.replace(' (Multinomial)', '').str.replace(' (Bernoulli)', '').str.replace(' (Linear)', '').str.replace(' (RBF)', '').str.replace(' (Basic)', '').str.replace(' (Optimized)', '')
        accuracy = comparison_df['Accuracy']
        colors = ['#2E8B57' if 'Naive Bayes' in model else '#FF6B6B' if 'SVM' in model else '#4ECDC4' for model in comparison_df['Model']]

        bars1 = ax1.bar(range(len(models)), accuracy, color=colors, alpha=0.8)
        ax1.set_title('Model Accuracy Comparison', fontweight='bold')
        ax1.set_ylabel('Accuracy')
        ax1.set_xticks(range(len(models)))
        ax1.set_xticklabels(models, rotation=45, ha='right')
        ax1.set_ylim(0.8, 1.02)

        # Add value labels on bars
        for bar, acc in zip(bars1, accuracy):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.005,
                    f'{acc:.3f}', ha='center', va='bottom', fontsize=9)

        # 2. Cross-Validation Scores with Error Bars
        ax2 = axes[0, 1]
        cv_means = comparison_df['CV Mean']
        cv_stds = comparison_df['CV Std']

        bars2 = ax2.bar(range(len(models)), cv_means, yerr=cv_stds,
                       color=colors, alpha=0.8, capsize=5)
        ax2.set_title('Cross-Validation Performance', fontweight='bold')
        ax2.set_ylabel('CV Accuracy')
        ax2.set_xticks(range(len(models)))
        ax2.set_xticklabels(models, rotation=45, ha='right')
        ax2.set_ylim(0.85, 1.0)

        # 3. Training Time Comparison
        ax3 = axes[1, 0]
        training_times = comparison_df['Training Time (s)']
        bars3 = ax3.bar(range(len(models)), training_times, color=colors, alpha=0.8)
        ax3.set_title('Training Time Comparison', fontweight='bold')
        ax3.set_ylabel('Training Time (seconds)')
        ax3.set_xticks(range(len(models)))
        ax3.set_xticklabels(models, rotation=45, ha='right')

        # Add value labels
        for bar, time_val in zip(bars3, training_times):
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2., height + 0.02,
                    f'{time_val:.3f}s', ha='center', va='bottom', fontsize=9)

        # 4. Precision vs Recall
        ax4 = axes[1, 1]
        precision = comparison_df['Precision']
        recall = comparison_df['Recall']

        scatter = ax4.scatter(precision, recall, c=range(len(models)),
                            s=100, alpha=0.8, cmap='viridis')
        ax4.set_title('Precision vs Recall', fontweight='bold')
        ax4.set_xlabel('Precision')
        ax4.set_ylabel('Recall')
        ax4.set_xlim(0.8, 1.02)
        ax4.set_ylim(0.8, 1.02)

        # Add model labels
        for i, model in enumerate(models):
            ax4.annotate(model, (precision.iloc[i], recall.iloc[i]),
                        xytext=(5, 5), textcoords='offset points', fontsize=8)

        plt.tight_layout()
        plt.savefig('ml_model_comparison.png', dpi=300, bbox_inches='tight')
        print("Visualization saved as 'ml_model_comparison.png'")
        plt.show()

def main():
    """Main function to run the comprehensive ML comparison"""

    # Find the latest dataset
    import glob
    import os

    dataset_files = glob.glob("enhanced_dataset_*.csv")
    if not dataset_files:
        dataset_files = glob.glob("*dataset*.csv")

    if not dataset_files:
        print("No dataset file found!")
        return

    latest_dataset = max(dataset_files, key=os.path.getctime)
    print(f"Using dataset: {latest_dataset}")

    # Run comprehensive comparison
    comparison = MLModelComparison(latest_dataset)
    comparison.load_and_prepare_data()
    comparison.analyze_text_characteristics()
    comparison.train_and_evaluate_models()
    comparison_df = comparison.detailed_performance_analysis()
    comparison.analyze_naive_bayes_advantages()

    # Create visualizations
    comparison.create_visualizations(comparison_df)

    print("\n" + "="*70)
    print("BUSINESS IMPACT ANALYSIS")
    print("="*70)
    print("For scam detection, model performance priorities:")
    print("1. PRECISION (minimize false positives):")
    print("   - Avoid blocking legitimate financial communications")
    print("   - Maintain customer trust and experience")
    print("   - Reduce manual review workload")

    print("\n2. RECALL (minimize false negatives):")
    print("   - Catch actual scams to prevent financial losses")
    print("   - Protect customers from fraud")
    print("   - Maintain regulatory compliance")

    print("\n3. F1-SCORE (balanced measure):")
    print("   - Optimal balance between precision and recall")
    print("   - Best overall performance indicator")
    print("   - Suitable for production deployment")

    print("\n" + "="*70)
    print("CONCLUSION")
    print("="*70)
    print("Naive Bayes typically excels in scam detection because:")
    print("1. Text classification naturally fits the independence assumption")
    print("2. Efficient handling of high-dimensional sparse text features")
    print("3. Robust performance with limited training data")
    print("4. Fast training and prediction times")
    print("5. Good baseline performance requiring minimal tuning")
    print("6. Probabilistic output useful for confidence-based decisions")
    print("7. Strong cross-validation performance indicating good generalization")

if __name__ == "__main__":
    main()
