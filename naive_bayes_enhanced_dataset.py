#!/usr/bin/env python3
"""
Naive Bayes Classifier for Enhanced Dataset Generator
Applies Naive <PERSON> algorithm to data generated by enhanced_dataset_generator.py
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.feature_extraction.text import TfidfVectorizer, CountVectorizer
from sklearn.naive_bayes import MultinomialNB, GaussianNB, BernoulliNB
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score, roc_curve, auc
from sklearn.pipeline import Pipeline
import re
from collections import Counter
from enhanced_dataset_generator import EnhancedDatasetGenerator
import warnings
warnings.filterwarnings('ignore')

# Use non-interactive backend for plots
import matplotlib
matplotlib.use('Agg')

class NaiveBayesEnhancedAnalysis:
    def __init__(self):
        """Initialize the Naive Bayes analysis"""
        self.generator = EnhancedDatasetGenerator()
        self.df = None
        self.X_train = None
        self.X_test = None
        self.y_train = None
        self.y_test = None
        self.models = {}
        self.best_model = None
        
    def generate_dataset(self, scale_factor=1):
        """Generate dataset using enhanced generator"""
        print("Generating dataset using EnhancedDatasetGenerator...")
        
        # Generate different types of data
        self.generator.generate_advance_fee_scams(30 * scale_factor)
        self.generator.generate_romance_scams(20 * scale_factor)
        self.generator.generate_investment_scams(25 * scale_factor)
        self.generator.generate_legitimate_messages(35 * scale_factor)
        
        # Convert to DataFrame
        self.df = pd.DataFrame(self.generator.data)
        
        # Create binary classification
        scam_labels = ['ADVANCE_FEE_SCAM', 'ROMANCE_SCAM', 'INVESTMENT_SCAM']
        self.df['is_scam'] = self.df['Label'].apply(lambda x: 1 if x in scam_labels else 0)
        
        # Add text features
        self.df['text_length'] = self.df['Text Message / Email / Chat'].str.len()
        self.df['word_count'] = self.df['Text Message / Email / Chat'].str.split().str.len()
        
        print(f"Generated {len(self.df)} records")
        print(f"Scam messages: {self.df['is_scam'].sum()}")
        print(f"Legitimate messages: {(self.df['is_scam'] == 0).sum()}")
        
        return self.df
    
    def preprocess_text(self):
        """Preprocess text data for Naive Bayes"""
        print("\nPreprocessing text data...")
        
        def clean_text(text):
            """Clean text for better classification"""
            if pd.isna(text):
                return ""
            
            # Convert to lowercase
            text = text.lower()
            
            # Remove URLs and emails
            text = re.sub(r'http\S+|www\S+|https\S+', '', text, flags=re.MULTILINE)
            text = re.sub(r'\S+@\S+', '', text)
            
            # Remove phone numbers
            text = re.sub(r'\+?\d[\d\s\-\(\)]{7,}\d', '', text)
            
            # Keep only alphanumeric and spaces
            text = re.sub(r'[^a-zA-Z0-9\s]', ' ', text)
            
            # Remove extra whitespace
            text = re.sub(r'\s+', ' ', text).strip()
            
            return text
        
        # Apply text cleaning
        self.df['cleaned_text'] = self.df['Text Message / Email / Chat'].apply(clean_text)
        
        # Remove very short messages
        self.df = self.df[self.df['cleaned_text'].str.len() >= 10].reset_index(drop=True)
        
        print(f"After preprocessing: {len(self.df)} messages remaining")
        
    def prepare_features(self):
        """Prepare features for Naive Bayes models"""
        print("\nPreparing features for Naive Bayes...")
        
        # Split the data
        X = self.df['cleaned_text']
        y = self.df['is_scam']
        
        self.X_train, self.X_test, self.y_train, self.y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        print(f"Training set: {len(self.X_train)} samples")
        print(f"Test set: {len(self.X_test)} samples")
        print(f"Training scam ratio: {self.y_train.mean():.2%}")
        print(f"Test scam ratio: {self.y_test.mean():.2%}")
    
    def train_naive_bayes_models(self):
        """Train different Naive Bayes variants"""
        print("\n" + "="*60)
        print("TRAINING NAIVE BAYES MODELS")
        print("="*60)
        
        # Define different Naive Bayes models with various vectorizers
        models = {
            'Multinomial NB + TF-IDF': Pipeline([
                ('tfidf', TfidfVectorizer(max_features=3000, stop_words='english', ngram_range=(1, 2))),
                ('nb', MultinomialNB(alpha=1.0))
            ]),
            'Multinomial NB + Count': Pipeline([
                ('count', CountVectorizer(max_features=3000, stop_words='english', ngram_range=(1, 2))),
                ('nb', MultinomialNB(alpha=1.0))
            ]),
            'Bernoulli NB + TF-IDF': Pipeline([
                ('tfidf', TfidfVectorizer(max_features=2000, stop_words='english', binary=True)),
                ('nb', BernoulliNB(alpha=1.0))
            ]),
            'Multinomial NB + TF-IDF (Optimized)': Pipeline([
                ('tfidf', TfidfVectorizer(max_features=5000, stop_words='english', 
                                        ngram_range=(1, 3), min_df=2, max_df=0.95)),
                ('nb', MultinomialNB(alpha=0.5))
            ])
        }
        
        results = {}
        
        for name, model in models.items():
            print(f"\nTraining {name}...")
            
            # Train the model
            model.fit(self.X_train, self.y_train)
            
            # Make predictions
            y_pred = model.predict(self.X_test)
            y_pred_proba = model.predict_proba(self.X_test)[:, 1]
            
            # Calculate metrics
            accuracy = accuracy_score(self.y_test, y_pred)
            
            # Cross-validation
            cv_scores = cross_val_score(model, self.X_train, self.y_train, cv=5, scoring='accuracy')
            
            results[name] = {
                'model': model,
                'accuracy': accuracy,
                'cv_mean': cv_scores.mean(),
                'cv_std': cv_scores.std(),
                'predictions': y_pred,
                'probabilities': y_pred_proba
            }
            
            print(f"  Test Accuracy: {accuracy:.4f}")
            print(f"  CV Accuracy: {cv_scores.mean():.4f} (+/- {cv_scores.std() * 2:.4f})")
        
        self.models = results
        
        # Find best model
        best_model_name = max(results.keys(), key=lambda x: results[x]['accuracy'])
        self.best_model = results[best_model_name]['model']
        
        print(f"\n🏆 Best Model: {best_model_name}")
        print(f"   Accuracy: {results[best_model_name]['accuracy']:.4f}")
        
        return results
    
    def evaluate_best_model(self):
        """Detailed evaluation of the best Naive Bayes model"""
        print("\n" + "="*60)
        print("DETAILED NAIVE BAYES EVALUATION")
        print("="*60)
        
        # Get best model results
        best_model_name = max(self.models.keys(), key=lambda x: self.models[x]['accuracy'])
        best_results = self.models[best_model_name]
        
        print(f"Best Model: {best_model_name}")
        print(f"Test Accuracy: {best_results['accuracy']:.4f}")
        
        # Classification report
        print("\nClassification Report:")
        print(classification_report(self.y_test, best_results['predictions'], 
                                  target_names=['Legitimate', 'Scam']))
        
        # Confusion matrix
        print("\nConfusion Matrix:")
        cm = confusion_matrix(self.y_test, best_results['predictions'])
        print(cm)
        
        # Feature importance (top words)
        if hasattr(self.best_model.named_steps.get('tfidf', None), 'get_feature_names_out'):
            print("\nTop 15 Most Important Features for Scam Detection:")
            vectorizer = self.best_model.named_steps['tfidf']
            nb_classifier = self.best_model.named_steps['nb']
            
            feature_names = vectorizer.get_feature_names_out()
            feature_log_prob = nb_classifier.feature_log_prob_
            
            # Get features most indicative of scam (class 1)
            scam_features = feature_log_prob[1] - feature_log_prob[0]
            top_scam_indices = scam_features.argsort()[-15:][::-1]
            
            for i, idx in enumerate(top_scam_indices, 1):
                print(f"  {i:2d}. {feature_names[idx]}: {scam_features[idx]:.4f}")
    
    def test_sample_predictions(self):
        """Test the model with sample messages"""
        print("\n" + "="*60)
        print("SAMPLE PREDICTIONS WITH NAIVE BAYES")
        print("="*60)
        
        # Test messages from the enhanced generator patterns
        test_messages = [
            # Advance fee scam patterns
            "I am Dr. Johnson from First Bank Lagos. We have $5,000,000 belonging to deceased client. You can claim as next of kin for 30% share.",
            "Greetings! I am Mrs. Sarah from Abuja. Due to political situation, I need to transfer $12,000,000. You will receive 25% for your assistance.",
            
            # Romance scam patterns
            "Hello my love, I am David from UK. I saw your profile and fell in love. I want to marry you and share my inheritance.",
            "Dearest, I am stuck in Syria and need $5000 to come to you. I will pay back when we meet my darling.",
            
            # Investment scam patterns
            "INVESTMENT OPPORTUNITY: Invest ₦50,000 in our forex trading and get ₦250,000 in 30 days. 500% guaranteed returns!",
            "CRYPTOCURRENCY: New NairaCoin launching. Invest ₦100,000 now and become millionaire in 6 months.",
            
            # Legitimate messages
            "Central Bank of Nigeria announces new exchange rate: USD/NGN 461.50. All authorized dealers must comply immediately.",
            "EFCC warns citizens against advance fee fraud. Always verify through official channels before making payments.",
            "SEC approves new mutual fund with minimum investment of ₦10,000. Fund targets agriculture sector."
        ]
        
        for i, message in enumerate(test_messages, 1):
            prediction = self.best_model.predict([message])[0]
            probability = self.best_model.predict_proba([message])[0]
            
            result = "SCAM" if prediction == 1 else "LEGITIMATE"
            confidence = max(probability)
            scam_prob = probability[1]
            
            print(f"\nTest {i}:")
            print(f"Message: {message[:80]}...")
            print(f"Prediction: {result}")
            print(f"Confidence: {confidence:.2%}")
            print(f"Scam Probability: {scam_prob:.2%}")
            
            # Risk assessment
            if scam_prob > 0.8:
                risk = "🔴 HIGH RISK"
            elif scam_prob > 0.6:
                risk = "🟡 MEDIUM RISK"
            elif scam_prob > 0.4:
                risk = "🟠 LOW RISK"
            else:
                risk = "🟢 SAFE"
            print(f"Risk Level: {risk}")
    
    def create_visualizations(self):
        """Create visualizations for Naive Bayes results"""
        print("\nCreating Naive Bayes visualizations...")
        
        plt.figure(figsize=(16, 12))
        
        # Model comparison
        plt.subplot(2, 3, 1)
        model_names = list(self.models.keys())
        accuracies = [self.models[name]['accuracy'] for name in model_names]
        cv_means = [self.models[name]['cv_mean'] for name in model_names]
        
        x = np.arange(len(model_names))
        width = 0.35
        
        bars1 = plt.bar(x - width/2, accuracies, width, label='Test Accuracy', alpha=0.8)
        bars2 = plt.bar(x + width/2, cv_means, width, label='CV Accuracy', alpha=0.8)
        
        plt.title('Naive Bayes Model Comparison', fontweight='bold')
        plt.xlabel('Models')
        plt.ylabel('Accuracy')
        plt.xticks(x, [name.split(' + ')[0] for name in model_names], rotation=45, ha='right')
        plt.legend()
        plt.ylim(0.7, 1.0)
        
        # Add value labels
        for bars in [bars1, bars2]:
            for bar in bars:
                height = bar.get_height()
                plt.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                        f'{height:.3f}', ha='center', va='bottom', fontsize=8)
        
        # Confusion Matrix
        plt.subplot(2, 3, 2)
        best_model_name = max(self.models.keys(), key=lambda x: self.models[x]['accuracy'])
        cm = confusion_matrix(self.y_test, self.models[best_model_name]['predictions'])
        
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                   xticklabels=['Legitimate', 'Scam'],
                   yticklabels=['Legitimate', 'Scam'])
        plt.title(f'Confusion Matrix\n{best_model_name}', fontweight='bold')
        plt.xlabel('Predicted')
        plt.ylabel('Actual')
        
        # ROC Curve
        plt.subplot(2, 3, 3)
        best_proba = self.models[best_model_name]['probabilities']
        fpr, tpr, _ = roc_curve(self.y_test, best_proba)
        roc_auc = auc(fpr, tpr)
        
        plt.plot(fpr, tpr, color='darkorange', lw=2, label=f'ROC curve (AUC = {roc_auc:.3f})')
        plt.plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--')
        plt.xlim([0.0, 1.0])
        plt.ylim([0.0, 1.05])
        plt.xlabel('False Positive Rate')
        plt.ylabel('True Positive Rate')
        plt.title('ROC Curve - Naive Bayes', fontweight='bold')
        plt.legend(loc="lower right")
        
        # Feature importance (if available)
        plt.subplot(2, 3, 4)
        if hasattr(self.best_model.named_steps.get('tfidf', None), 'get_feature_names_out'):
            vectorizer = self.best_model.named_steps['tfidf']
            nb_classifier = self.best_model.named_steps['nb']
            
            feature_names = vectorizer.get_feature_names_out()
            feature_log_prob = nb_classifier.feature_log_prob_
            
            # Get top scam features
            scam_features = feature_log_prob[1] - feature_log_prob[0]
            top_indices = scam_features.argsort()[-10:]
            
            top_features = [feature_names[i] for i in top_indices]
            top_scores = [scam_features[i] for i in top_indices]
            
            plt.barh(range(len(top_features)), top_scores, color='red', alpha=0.7)
            plt.title('Top 10 Scam Indicators', fontweight='bold')
            plt.xlabel('Feature Importance')
            plt.yticks(range(len(top_features)), top_features)
        
        # Data distribution
        plt.subplot(2, 3, 5)
        label_counts = self.df['Label'].value_counts()
        colors = ['#e74c3c', '#3498db', '#2ecc71', '#f39c12']
        
        plt.pie(label_counts.values, labels=label_counts.index, colors=colors,
                autopct='%1.1f%%', startangle=90)
        plt.title('Enhanced Dataset Distribution', fontweight='bold')
        
        # Performance metrics
        plt.subplot(2, 3, 6)
        best_pred = self.models[best_model_name]['predictions']
        
        # Calculate metrics
        tn, fp, fn, tp = cm.ravel()
        precision = tp / (tp + fp)
        recall = tp / (tp + fn)
        f1 = 2 * (precision * recall) / (precision + recall)
        specificity = tn / (tn + fp)
        
        metrics = ['Accuracy', 'Precision', 'Recall', 'F1-Score', 'Specificity']
        values = [self.models[best_model_name]['accuracy'], precision, recall, f1, specificity]
        
        bars = plt.bar(metrics, values, color=['blue', 'green', 'orange', 'red', 'purple'], alpha=0.7)
        plt.title('Performance Metrics', fontweight='bold')
        plt.ylabel('Score')
        plt.ylim(0, 1)
        
        # Add value labels
        for bar, value in zip(bars, values):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                    f'{value:.3f}', ha='center', va='bottom', fontweight='bold')
        
        plt.tight_layout()
        plt.savefig('naive_bayes_enhanced_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("   ✓ Naive Bayes analysis saved as 'naive_bayes_enhanced_analysis.png'")
    
    def run_complete_analysis(self):
        """Run the complete Naive Bayes analysis"""
        print("="*70)
        print("NAIVE BAYES ANALYSIS - ENHANCED DATASET GENERATOR")
        print("="*70)
        
        # Generate dataset
        self.generate_dataset(scale_factor=2)  # Generate more data for better training
        
        # Preprocess
        self.preprocess_text()
        
        # Prepare features
        self.prepare_features()
        
        # Train models
        self.train_naive_bayes_models()
        
        # Evaluate
        self.evaluate_best_model()
        
        # Test samples
        self.test_sample_predictions()
        
        # Create visualizations
        self.create_visualizations()
        
        print("\n" + "="*70)
        print("NAIVE BAYES ANALYSIS COMPLETE!")
        print("="*70)
        print(f"✓ Dataset generated: {len(self.df)} records")
        print(f"✓ Best model accuracy: {max(self.models.values(), key=lambda x: x['accuracy'])['accuracy']:.2%}")
        print("✓ Visualization saved: naive_bayes_enhanced_analysis.png")
        print("✓ Ready for scam detection!")

def main():
    """Main function to run Naive Bayes analysis"""
    analyzer = NaiveBayesEnhancedAnalysis()
    analyzer.run_complete_analysis()

if __name__ == "__main__":
    main()
