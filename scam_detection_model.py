#!/usr/bin/env python3
"""
Nigerian Scam Detection Model using Pandas and Scikit-Learn
Trains machine learning models to classify scam vs legitimate messages
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.feature_extraction.text import TfidfVectorizer, CountVectorizer
from sklearn.naive_bayes import MultinomialNB
from sklearn.linear_model import LogisticRegression
from sklearn.ensemble import RandomForestClassifier
from sklearn.svm import SVC
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
from sklearn.pipeline import Pipeline
import re
import string
from collections import Counter
import warnings
warnings.filterwarnings('ignore')

class ScamDetectionModel:
    def __init__(self, dataset_path):
        """Initialize the scam detection model"""
        self.dataset_path = dataset_path
        self.df = None
        self.X_train = None
        self.X_test = None
        self.y_train = None
        self.y_test = None
        self.models = {}
        self.best_model = None
        
    def load_and_explore_data(self):
        """Load and explore the dataset"""
        print("Loading dataset...")
        self.df = pd.read_csv(self.dataset_path)
        
        print(f"Dataset shape: {self.df.shape}")
        print(f"Columns: {list(self.df.columns)}")
        
        # Basic info
        print("\n" + "="*50)
        print("DATASET OVERVIEW")
        print("="*50)
        print(self.df.info())
        
        # Check for missing values
        print("\nMissing values:")
        print(self.df.isnull().sum())
        
        # Label distribution
        print("\nLabel distribution:")
        label_counts = self.df['Label'].value_counts()
        print(label_counts)
        
        # Show percentage distribution
        print("\nLabel percentage distribution:")
        label_percentages = (self.df['Label'].value_counts(normalize=True) * 100).round(2)
        print(label_percentages)
        
        return self.df
    
    def preprocess_data(self):
        """Preprocess the data for machine learning"""
        print("\n" + "="*50)
        print("DATA PREPROCESSING")
        print("="*50)
        
        # Create binary classification: Scam vs Legitimate
        scam_labels = ['ADVANCE_FEE_SCAM', 'ROMANCE_SCAM', 'INVESTMENT_SCAM', 'SCAM_SAMPLE', 'SCAM_ALERT', 'SCAM_WARNING']
        legitimate_labels = ['LEGITIMATE_FINANCIAL', 'LEGITIMATE_SAMPLE', 'OFFICIAL_ALERT', 'FRAUD_AWARENESS', 
                           'CONSUMER_PROTECTION', 'COMPLAINT_PROCEDURE', 'CORRUPTION_ALERT']
        
        # Create binary target variable
        self.df['is_scam'] = self.df['Label'].apply(lambda x: 1 if x in scam_labels else 0)
        
        print(f"Binary classification distribution:")
        print(f"Scam (1): {self.df['is_scam'].sum()} messages")
        print(f"Legitimate (0): {(self.df['is_scam'] == 0).sum()} messages")
        
        # Text preprocessing function
        def clean_text(text):
            """Clean and preprocess text"""
            if pd.isna(text):
                return ""
            
            # Convert to lowercase
            text = text.lower()
            
            # Remove URLs
            text = re.sub(r'http\S+|www\S+|https\S+', '', text, flags=re.MULTILINE)
            
            # Remove email addresses
            text = re.sub(r'\S+@\S+', '', text)
            
            # Remove phone numbers
            text = re.sub(r'\+?\d[\d\s\-\(\)]{7,}\d', '', text)
            
            # Remove extra whitespace
            text = re.sub(r'\s+', ' ', text).strip()
            
            return text
        
        # Apply text cleaning
        self.df['cleaned_text'] = self.df['Text Message / Email / Chat'].apply(clean_text)
        
        # Remove very short messages (less than 10 characters)
        self.df = self.df[self.df['cleaned_text'].str.len() >= 10].reset_index(drop=True)
        
        print(f"After preprocessing: {len(self.df)} messages remaining")
        
        return self.df
    
    def analyze_text_features(self):
        """Analyze text features and patterns"""
        print("\n" + "="*50)
        print("TEXT ANALYSIS")
        print("="*50)
        
        # Text length analysis
        self.df['text_length'] = self.df['cleaned_text'].str.len()
        self.df['word_count'] = self.df['cleaned_text'].str.split().str.len()
        
        print("Text length statistics:")
        print(self.df.groupby('is_scam')[['text_length', 'word_count']].describe())
        
        # Common words in scam vs legitimate messages
        scam_text = ' '.join(self.df[self.df['is_scam'] == 1]['cleaned_text'])
        legit_text = ' '.join(self.df[self.df['is_scam'] == 0]['cleaned_text'])
        
        # Remove common stop words and punctuation
        stop_words = {'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'a', 'an', 'this', 'that', 'these', 'those'}
        
        def get_top_words(text, n=10):
            words = re.findall(r'\b[a-z]+\b', text.lower())
            words = [w for w in words if w not in stop_words and len(w) > 2]
            return Counter(words).most_common(n)
        
        print("\nTop words in SCAM messages:")
        scam_words = get_top_words(scam_text, 15)
        for word, count in scam_words:
            print(f"  {word}: {count}")
        
        print("\nTop words in LEGITIMATE messages:")
        legit_words = get_top_words(legit_text, 15)
        for word, count in legit_words:
            print(f"  {word}: {count}")
    
    def prepare_features(self):
        """Prepare features for machine learning"""
        print("\n" + "="*50)
        print("FEATURE PREPARATION")
        print("="*50)
        
        # Split the data
        X = self.df['cleaned_text']
        y = self.df['is_scam']
        
        self.X_train, self.X_test, self.y_train, self.y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        print(f"Training set: {len(self.X_train)} samples")
        print(f"Test set: {len(self.X_test)} samples")
        print(f"Training set scam ratio: {self.y_train.mean():.2%}")
        print(f"Test set scam ratio: {self.y_test.mean():.2%}")
    
    def train_models(self):
        """Train multiple machine learning models"""
        print("\n" + "="*50)
        print("MODEL TRAINING")
        print("="*50)
        
        # Define models with different vectorizers
        models = {
            'Naive Bayes (TF-IDF)': Pipeline([
                ('tfidf', TfidfVectorizer(max_features=5000, stop_words='english', ngram_range=(1, 2))),
                ('nb', MultinomialNB())
            ]),
            'Logistic Regression (TF-IDF)': Pipeline([
                ('tfidf', TfidfVectorizer(max_features=5000, stop_words='english', ngram_range=(1, 2))),
                ('lr', LogisticRegression(random_state=42, max_iter=1000))
            ]),
            'Random Forest (TF-IDF)': Pipeline([
                ('tfidf', TfidfVectorizer(max_features=3000, stop_words='english', ngram_range=(1, 2))),
                ('rf', RandomForestClassifier(n_estimators=100, random_state=42))
            ]),
            'SVM (TF-IDF)': Pipeline([
                ('tfidf', TfidfVectorizer(max_features=3000, stop_words='english', ngram_range=(1, 2))),
                ('svm', SVC(kernel='linear', random_state=42))
            ])
        }
        
        # Train and evaluate models
        results = {}
        
        for name, model in models.items():
            print(f"\nTraining {name}...")
            
            # Train the model
            model.fit(self.X_train, self.y_train)
            
            # Make predictions
            y_pred = model.predict(self.X_test)
            
            # Calculate accuracy
            accuracy = accuracy_score(self.y_test, y_pred)
            
            # Cross-validation score
            cv_scores = cross_val_score(model, self.X_train, self.y_train, cv=5, scoring='accuracy')
            
            results[name] = {
                'model': model,
                'accuracy': accuracy,
                'cv_mean': cv_scores.mean(),
                'cv_std': cv_scores.std(),
                'predictions': y_pred
            }
            
            print(f"  Test Accuracy: {accuracy:.4f}")
            print(f"  CV Accuracy: {cv_scores.mean():.4f} (+/- {cv_scores.std() * 2:.4f})")
        
        self.models = results
        
        # Find best model
        best_model_name = max(results.keys(), key=lambda x: results[x]['accuracy'])
        self.best_model = results[best_model_name]['model']
        
        print(f"\nBest model: {best_model_name} with accuracy: {results[best_model_name]['accuracy']:.4f}")
        
        return results
    
    def evaluate_best_model(self):
        """Detailed evaluation of the best model"""
        print("\n" + "="*50)
        print("DETAILED MODEL EVALUATION")
        print("="*50)
        
        # Get best model predictions
        best_model_name = max(self.models.keys(), key=lambda x: self.models[x]['accuracy'])
        best_predictions = self.models[best_model_name]['predictions']
        
        print(f"Best Model: {best_model_name}")
        print(f"Test Accuracy: {self.models[best_model_name]['accuracy']:.4f}")
        
        # Classification report
        print("\nClassification Report:")
        print(classification_report(self.y_test, best_predictions, 
                                  target_names=['Legitimate', 'Scam']))
        
        # Confusion matrix
        print("\nConfusion Matrix:")
        cm = confusion_matrix(self.y_test, best_predictions)
        print(cm)
        
        # Feature importance (if available)
        if hasattr(self.best_model.named_steps.get('rf', None), 'feature_importances_'):
            print("\nTop 10 Most Important Features (Random Forest):")
            vectorizer = self.best_model.named_steps['tfidf']
            feature_names = vectorizer.get_feature_names_out()
            importances = self.best_model.named_steps['rf'].feature_importances_
            
            feature_importance = list(zip(feature_names, importances))
            feature_importance.sort(key=lambda x: x[1], reverse=True)
            
            for feature, importance in feature_importance[:10]:
                print(f"  {feature}: {importance:.4f}")
    
    def test_predictions(self):
        """Test the model with sample messages"""
        print("\n" + "="*50)
        print("SAMPLE PREDICTIONS")
        print("="*50)
        
        # Sample test messages
        test_messages = [
            "Congratulations! You have won $1,000,000 in the lottery. Send your bank details to claim.",
            "Central Bank of Nigeria announces new exchange rate of 461.50 NGN per USD.",
            "URGENT: Your account will be suspended. Click here to verify your details immediately.",
            "EFCC warns citizens against advance fee fraud. Always verify before making payments.",
            "Investment opportunity: Double your money in 30 days with guaranteed returns.",
            "Nigerian Stock Exchange recorded 2.3% gain in market capitalization this week."
        ]
        
        for i, message in enumerate(test_messages, 1):
            prediction = self.best_model.predict([message])[0]
            probability = self.best_model.predict_proba([message])[0]
            
            result = "SCAM" if prediction == 1 else "LEGITIMATE"
            confidence = max(probability)
            
            print(f"\nTest {i}:")
            print(f"Message: {message}")
            print(f"Prediction: {result} (Confidence: {confidence:.2%})")

def main():
    """Main function to run the scam detection pipeline"""
    
    # Find the latest dataset file
    import glob
    import os
    
    dataset_files = glob.glob("nigerian_scam_dataset_*.csv")
    if not dataset_files:
        dataset_files = glob.glob("scam_dataset_*.csv")
    
    if not dataset_files:
        print("No dataset file found! Please run the scraper first.")
        return
    
    # Use the most recent dataset
    latest_dataset = max(dataset_files, key=os.path.getctime)
    print(f"Using dataset: {latest_dataset}")
    
    # Initialize and run the model
    model = ScamDetectionModel(latest_dataset)
    
    # Run the complete pipeline
    model.load_and_explore_data()
    model.preprocess_data()
    model.analyze_text_features()
    model.prepare_features()
    model.train_models()
    model.evaluate_best_model()
    model.test_predictions()
    
    print("\n" + "="*50)
    print("SCAM DETECTION MODEL TRAINING COMPLETE!")
    print("="*50)
    print("The model is now ready to classify Nigerian scam messages.")
    print("You can use the best model to predict new messages.")

if __name__ == "__main__":
    main()
