Model,Test_Accuracy,Precision,Recall,F1_Score,CV_Mean,CV_Std,Training_Time,Prediction_Time,Model_Object
RF Basic,0.9166666666666666,0.9230769230769231,0.8,0.8571428571428571,0.9369770580296896,0.03951282417713432,0.7928438186645508,0.06368780136108398,"Pipeline(steps=[('tfidf',
                 TfidfVectorizer(max_features=1000, stop_words='english')),
                ('rf',
                 RandomForestClassifier(n_estimators=50, random_state=42))])"
RF Optimized,0.9166666666666666,0.9230769230769231,0.8,0.8571428571428571,0.8896086369770579,0.04237516869095814,0.8595938682556152,0.05073690414428711,"Pipeline(steps=[('tfidf',
                 TfidfVectorizer(max_features=2000, ngram_range=(1, 2),
                                 stop_words='english')),
                ('rf',
                 RandomForestClassifier(max_depth=15, min_samples_split=5,
                                        random_state=42))])"
RF Deep,0.8958333333333334,0.8571428571428571,0.8,0.8275862068965518,0.9369770580296896,0.03951282417713432,2.440108060836792,0.1902010440826416,"Pipeline(steps=[('tfidf',
                 TfidfVectorizer(max_features=1500, stop_words='english')),
                ('rf',
                 RandomForestClassifier(max_depth=20, n_estimators=200,
                                        random_state=42))])"
RF Balanced,0.875,0.9090909090909091,0.6666666666666666,0.7692307692307692,0.8844804318488528,0.036174893968416125,0.6602737903594971,0.03100419044494629,"Pipeline(steps=[('tfidf',
                 TfidfVectorizer(max_features=1200, ngram_range=(1, 2),
                                 stop_words='english')),
                ('rf',
                 RandomForestClassifier(class_weight='balanced', max_depth=10,
                                        min_samples_split=10,
                                        random_state=42))])"
