#!/usr/bin/env python3
"""
Web Scraper for Nigerian Financial and Scam Dataset
Extracts text content from various Nigerian financial and scam-related websites
Output format: ID, Text Message/Email/Chat, Label
"""

import requests
from bs4 import BeautifulSoup
import pandas as pd
import time
import random
from urllib.parse import urljoin, urlparse
import logging
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import csv
import os
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ScamDatasetScraper:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        self.data = []
        self.id_counter = 1
        
        # Website configurations - Enhanced with more Nigerian sources
        self.websites = {
            # Original sources
            'nccc_reports': {
                'url': 'https://nccc.npf.gov.ng/ereport/signin',
                'label': 'SCAM_REPORT',
                'method': 'selenium'
            },
            'cbn_financial': {
                'url': 'https://www.cbn.gov.ng/rates/FinancialData.html',
                'label': 'LEGITIMATE_FINANCIAL',
                'method': 'requests'
            },
            'scamwatch': {
                'url': 'https://scamwatch.ng/scam/investment/investment-scam',
                'label': 'SCAM_WARNING',
                'method': 'requests'
            },
            'efcc_alerts': {
                'url': 'https://www.efcc.gov.ng/efcc/records/efcc-alert',
                'label': 'SCAM_ALERT',
                'method': 'requests'
            },

            # Additional Nigerian NGO/Legal/Government sources
            'nccc_news': {
                'url': 'https://nccc.npf.gov.ng/news',
                'label': 'SCAM_ALERT',
                'method': 'requests'
            },
            'icpc_reports': {
                'url': 'https://icpc.gov.ng/',
                'label': 'CORRUPTION_ALERT',
                'method': 'requests'
            },
            'fccpc_consumer': {
                'url': 'https://fccpc.gov.ng/',
                'label': 'CONSUMER_PROTECTION',
                'method': 'requests'
            },
            'cbn_fraud_awareness': {
                'url': 'https://www.cbn.gov.ng/supervision/cpdfraudandscam.html',
                'label': 'FRAUD_AWARENESS',
                'method': 'requests'
            },
            'sec_investment_scam': {
                'url': 'https://sec.gov.ng/category/investment-scam/',
                'label': 'INVESTMENT_SCAM',
                'method': 'requests'
            },
            'efcc_channels': {
                'url': 'https://www.efcc.gov.ng/efcc/channels-of-reporting-complaints-2',
                'label': 'COMPLAINT_PROCEDURE',
                'method': 'requests'
            },
            'cbn_consumer_protection': {
                'url': 'https://www.cbn.gov.ng/contacts/',
                'label': 'LEGITIMATE_FINANCIAL',
                'method': 'requests'
            }
        }

    def setup_selenium_driver(self):
        """Setup Chrome driver with appropriate options"""
        chrome_options = Options()
        chrome_options.add_argument('--headless')  # Run in background
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        
        try:
            driver = webdriver.Chrome(options=chrome_options)
            return driver
        except Exception as e:
            logger.error(f"Failed to setup Chrome driver: {e}") 
            logger.info("Please install ChromeDriver and ensure it's in PATH")
            return None

    def scrape_with_requests(self, url, label):
        """Scrape static content using requests and BeautifulSoup"""
        try:
            logger.info(f"Scraping {url} with requests...")
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()
            
            # Extract text content based on common patterns
            text_elements = []
            
            # Look for common content containers
            content_selectors = [
                'article', 'main', '.content', '#content', '.post', '.article',
                '.news-item', '.alert', '.warning', '.report', 'p', 'div.text'
            ]
            
            for selector in content_selectors:
                elements = soup.select(selector)
                for element in elements:
                    text = element.get_text(strip=True)
                    if len(text) > 50:  # Filter out short/irrelevant text
                        text_elements.append(text)
            
            # If no specific content found, get all paragraphs
            if not text_elements:
                paragraphs = soup.find_all('p')
                text_elements = [p.get_text(strip=True) for p in paragraphs if len(p.get_text(strip=True)) > 50]
            
            # Add to dataset
            for text in text_elements[:10]:  # Limit to first 10 relevant texts per page
                self.add_to_dataset(text, label)
                
            logger.info(f"Extracted {len(text_elements)} text elements from {url}")
            
        except requests.RequestException as e:
            logger.error(f"Error scraping {url}: {e}")
        except Exception as e:
            logger.error(f"Unexpected error scraping {url}: {e}")

    def scrape_with_selenium(self, url, label):
        """Scrape dynamic content using Selenium"""
        driver = self.setup_selenium_driver()
        if not driver:
            logger.error("Cannot proceed with Selenium scraping - driver setup failed")
            return
            
        try:
            logger.info(f"Scraping {url} with Selenium...")
            driver.get(url)
            
            # Wait for page to load
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # Look for text content
            text_elements = []
            
            # Try different selectors for content
            selectors = [
                "//article//text()[normalize-space()]",
                "//main//text()[normalize-space()]",
                "//div[@class='content']//text()[normalize-space()]",
                "//p[string-length(text()) > 50]",
                "//div[contains(@class, 'alert')]//text()[normalize-space()]",
                "//div[contains(@class, 'report')]//text()[normalize-space()]"
            ]
            
            for selector in selectors:
                try:
                    elements = driver.find_elements(By.XPATH, selector)
                    for element in elements:
                        text = element.text.strip() if hasattr(element, 'text') else str(element).strip()
                        if len(text) > 50:
                            text_elements.append(text)
                except:
                    continue
            
            # If still no content, get all visible text
            if not text_elements:
                body = driver.find_element(By.TAG_NAME, "body")
                full_text = body.text
                # Split into sentences/paragraphs
                sentences = [s.strip() for s in full_text.split('\n') if len(s.strip()) > 50]
                text_elements.extend(sentences[:10])
            
            # Add to dataset
            for text in text_elements[:10]:  # Limit to first 10 relevant texts
                self.add_to_dataset(text, label)
                
            logger.info(f"Extracted {len(text_elements)} text elements from {url}")
            
        except TimeoutException:
            logger.error(f"Timeout loading {url}")
        except Exception as e:
            logger.error(f"Error scraping {url} with Selenium: {e}")
        finally:
            driver.quit()

    def add_to_dataset(self, text, label):
        """Add extracted text to dataset"""
        # Clean and validate text
        text = text.replace('\n', ' ').replace('\r', ' ').strip()
        text = ' '.join(text.split())  # Remove extra whitespace
        
        if len(text) > 20:  # Minimum text length
            self.data.append({
                'ID': self.id_counter,
                'Text Message / Email / Chat': text,
                'Label': label
            })
            self.id_counter += 1

    def scrape_all_websites(self):
        """Scrape all configured websites"""
        logger.info("Starting web scraping for dataset creation...")
        
        for site_name, config in self.websites.items():
            logger.info(f"Processing {site_name}...")
            
            if config['method'] == 'requests':
                self.scrape_with_requests(config['url'], config['label'])
            elif config['method'] == 'selenium':
                self.scrape_with_selenium(config['url'], config['label'])
            
            # Add delay between requests to be respectful
            time.sleep(random.uniform(2, 5))

    def save_dataset(self, filename='scam_dataset.csv'):
        """Save the scraped dataset to CSV file"""
        if not self.data:
            logger.warning("No data to save!")
            return
            
        df = pd.DataFrame(self.data)
        df.to_csv(filename, index=False, encoding='utf-8')
        logger.info(f"Dataset saved to {filename} with {len(self.data)} records")
        
        # Also save as JSON for backup
        json_filename = filename.replace('.csv', '.json')
        df.to_json(json_filename, orient='records', indent=2)
        logger.info(f"Dataset also saved to {json_filename}")

    def print_sample_data(self, n=5):
        """Print sample of scraped data"""
        if self.data:
            logger.info(f"Sample of scraped data (first {min(n, len(self.data))} records):")
            for i, record in enumerate(self.data[:n]):
                print(f"\nRecord {i+1}:")
                print(f"ID: {record['ID']}")
                print(f"Text: {record['Text Message / Email / Chat'][:100]}...")
                print(f"Label: {record['Label']}")

    def add_comprehensive_sample_data(self):
        """Add comprehensive sample data to reach 200+ rows"""

        # Nigerian-specific scam patterns
        nigerian_scam_data = [
            "Congratulations! You have won $1,000,000 in the Nigerian National Lottery. Send your bank details to claim your prize.",
            "URGENT: Your account will be closed unless you verify your details immediately. Click this link and enter your password.",
            "Investment opportunity: Double your money in 30 days with our guaranteed forex trading system. Minimum investment ₦50,000.",
            "You have inherited $5 million from a distant relative. Pay processing fee of $500 to receive your inheritance.",
            "Hello, I am Mrs. Sarah Johnson from UK. I want to transfer $2.5 million to your account. Reply with your bank details.",
            "BREAKING: CBN is giving out ₦500,000 to first 1000 applicants. Click here to apply now!",
            "Your ATM card is ready for collection at our office. Pay ₦5,000 delivery fee to receive ₦2,000,000.",
            "Urgent business proposal: Help us transfer $15 million from dormant account. You get 30% commission.",
            "EFCC has frozen your account due to suspicious activity. Pay ₦10,000 fine to unfreeze immediately.",
            "You've been selected for a government empowerment program. Pay ₦3,000 registration fee for ₦100,000 grant.",
            "Investment alert: Buy Bitcoin through our platform and get 500% returns in 7 days guaranteed.",
            "Your phone number won ₦1,000,000 in MTN promo. Send ₦2,000 for processing to claim your prize.",
            "SCAM ALERT: Fake recruitment into Nigerian Army. Pay ₦50,000 for guaranteed admission.",
            "Romance scam: I love you and want to marry you. Send money for my visa to come to Nigeria.",
            "Fake loan offer: Get ₦5,000,000 loan without collateral. Pay ₦20,000 processing fee first.",
            "Ponzi scheme: Invest ₦10,000 today and get ₦50,000 in 30 days. Limited time offer!",
            "Fake cryptocurrency: New Nigerian coin launching. Invest now and become millionaire in 6 months.",
            "Employment scam: Guaranteed job at NNPC. Pay ₦100,000 for interview slot and documentation.",
            "Fake scholarship: Full scholarship to study abroad. Pay ₦25,000 application fee immediately.",
            "Medical scam: Miracle cure for diabetes and hypertension. Pay ₦15,000 for herbal medicine.",
            "Real estate fraud: Buy land in Abuja for ₦500,000. Triple your money in 2 years guaranteed.",
            "Fake charity: Donate ₦5,000 to help orphans and God will bless you with ₦50,000.",
            "Online shopping scam: iPhone 14 for ₦50,000. Pay now and receive in 24 hours.",
            "Fake insurance: Insure your life for ₦1,000 and get ₦10,000,000 if anything happens.",
            "Pyramid scheme: Refer 5 people and earn ₦100,000 monthly. Join our network marketing.",
            "Fake forex trading: Learn forex and earn $1000 daily. Pay ₦30,000 for training materials.",
            "Ritual money scam: Use this spiritual soap and become rich overnight. Pay ₦8,000 only.",
            "Fake visa lottery: Win American visa lottery. Pay ₦15,000 processing fee to claim.",
            "Online betting scam: Sure odds that never fail. Pay ₦5,000 for VIP predictions.",
            "Fake oil deal: Partner with us in crude oil business. Invest ₦1,000,000 for huge returns."
        ]

        # Legitimate financial and government communications
        legitimate_data = [
            "Central Bank of Nigeria announces new monetary policy rate of 18.75% effective immediately.",
            "CBN foreign exchange rates: USD/NGN 461.50, GBP/NGN 570.25, EUR/NGN 495.80 as of today.",
            "The Nigerian Stock Exchange recorded a 2.3% gain in market capitalization this week.",
            "Banks are required to maintain a minimum capital adequacy ratio of 15% according to CBN guidelines.",
            "Financial inclusion rate in Nigeria reaches 64.1% according to latest CBN survey.",
            "EFCC recovers ₦5 billion from corrupt officials in anti-graft operations this quarter.",
            "ICPC launches new online portal for reporting corruption cases across Nigeria.",
            "Federal Competition and Consumer Protection Commission warns against fake products.",
            "Securities and Exchange Commission approves new mutual fund for retail investors.",
            "Nigeria Deposit Insurance Corporation increases deposit insurance coverage to ₦500,000.",
            "CBN introduces new guidelines for digital banking services in Nigeria.",
            "FIRS announces new tax payment platform for improved revenue collection.",
            "Nigerian Communications Commission warns against SIM card registration scams.",
            "PENCOM releases new guidelines for pension fund administrators.",
            "NAICOM issues circular on insurance premium payment procedures.",
            "Federal Ministry of Finance announces budget allocation for infrastructure development.",
            "Nigeria Customs Service introduces new trade facilitation measures.",
            "NERC approves new electricity tariff structure for distribution companies.",
            "Nigerian Maritime Administration warns against fake seafarer certificates.",
            "Corporate Affairs Commission launches online business registration portal.",
            "National Bureau of Statistics releases inflation rate data for current month.",
            "Nigeria Police Force establishes cybercrime reporting centers nationwide.",
            "Federal Road Safety Corps announces new driver's license requirements.",
            "Nigerian Investment Promotion Commission promotes foreign direct investment.",
            "Standards Organisation of Nigeria issues product quality certification guidelines."
        ]

        # Official alerts and warnings
        official_alerts = [
            "EFCC ALERT: Beware of fake investment schemes promising unrealistic returns. Always verify before investing.",
            "CBN WARNING: Do not share your BVN or account details with unauthorized persons.",
            "ICPC NOTICE: Report corruption cases through our official channels only.",
            "FCCPC ALERT: Verify product authenticity before purchase to avoid counterfeit goods.",
            "SEC WARNING: Only invest through registered capital market operators.",
            "NPF ADVISORY: Report cybercrime incidents through official police channels.",
            "NDIC NOTICE: Bank deposits are insured up to ₦500,000 per depositor.",
            "NCC WARNING: Beware of fake SIM card registration and phone number scams.",
            "PENCOM ALERT: Verify pension fund administrator credentials before investment.",
            "NAICOM NOTICE: Purchase insurance only from licensed insurance companies.",
            "CAC ADVISORY: Register businesses through official Corporate Affairs Commission portal.",
            "FIRS WARNING: Pay taxes only through authorized collection channels.",
            "NERC ALERT: Report electricity billing disputes through proper channels.",
            "NIMASA NOTICE: Verify seafarer training certificates through official database.",
            "FRSC WARNING: Obtain driver's license only through authorized centers.",
            "NIPC ADVISORY: Foreign investors should follow official investment procedures.",
            "SON ALERT: Check product standards compliance before purchase.",
            "NBS NOTICE: Official statistics are published only through authorized channels.",
            "Customs WARNING: Declare goods properly to avoid penalties and seizure.",
            "Finance Ministry ALERT: Government grants are processed through official channels only."
        ]

        # Add all sample data with appropriate labels
        for text in nigerian_scam_data:
            self.add_to_dataset(text, "SCAM_SAMPLE")

        for text in legitimate_data:
            self.add_to_dataset(text, "LEGITIMATE_SAMPLE")

        for text in official_alerts:
            self.add_to_dataset(text, "OFFICIAL_ALERT")

        logger.info(f"Added comprehensive sample data: {len(nigerian_scam_data + legitimate_data + official_alerts)} records")

def main():
    """Main function to run the scraper"""
    scraper = ScamDatasetScraper()

    try:
        # Add comprehensive sample data first (to ensure we have enough data)
        scraper.add_comprehensive_sample_data()

        # Scrape all websites
        scraper.scrape_all_websites()

        # Print sample data
        scraper.print_sample_data()

        # Save dataset
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"scam_dataset_{timestamp}.csv"
        scraper.save_dataset(filename)

        # Print detailed summary
        print(f"\n{'='*60}")
        print(f"NIGERIAN SCAM DATASET - SCRAPING SUMMARY")
        print(f"{'='*60}")
        print(f"Total records collected: {len(scraper.data)}")
        print(f"Target: 200+ rows ✓" if len(scraper.data) >= 200 else f"Target: 200+ rows (Current: {len(scraper.data)})")
        print(f"Dataset saved as: {filename}")

        # Show label distribution
        label_counts = {}
        for record in scraper.data:
            label = record['Label']
            label_counts[label] = label_counts.get(label, 0) + 1

        print(f"\nLabel Distribution:")
        for label, count in sorted(label_counts.items()):
            print(f"  {label}: {count} records")

        print(f"{'='*60}")

    except KeyboardInterrupt:
        logger.info("Scraping interrupted by user")
    except Exception as e:
        logger.error(f"Error during scraping: {e}")
    finally:
        logger.info("Scraping completed")

if __name__ == "__main__":
    main()
