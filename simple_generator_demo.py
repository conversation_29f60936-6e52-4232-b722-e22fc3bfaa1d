#!/usr/bin/env python3
"""
Simple demo of the Enhanced Dataset Generator
"""

from enhanced_dataset_generator import EnhancedDatasetGenerator

def main():
    print("ENHANCED DATASET GENERATOR DEMO")
    print("="*50)
    
    generator = EnhancedDatasetGenerator()
    
    # Generate samples
    print("\n🔴 ADVANCE FEE SCAM SAMPLES:")
    print("-" * 40)
    generator.generate_advance_fee_scams(3)
    for i, record in enumerate(generator.data[-3:], 1):
        print(f"\n{i}. {record['Text Message / Email / Chat']}")
    
    print("\n\n💕 ROMANCE SCAM SAMPLES:")
    print("-" * 40)
    generator.generate_romance_scams(2)
    for i, record in enumerate(generator.data[-2:], 1):
        print(f"\n{i}. {record['Text Message / Email / Chat']}")
    
    print("\n\n💰 INVESTMENT SCAM SAMPLES:")
    print("-" * 40)
    generator.generate_investment_scams(2)
    for i, record in enumerate(generator.data[-2:], 1):
        print(f"\n{i}. {record['Text Message / Email / Chat']}")
    
    print("\n\n✅ LEGITIMATE SAMPLES:")
    print("-" * 40)
    generator.generate_legitimate_messages(2)
    for i, record in enumerate(generator.data[-2:], 1):
        print(f"\n{i}. {record['Text Message / Email / Chat']}")
    
    print(f"\n\n📊 TOTAL GENERATED: {len(generator.data)} records")
    print("\n🎯 VISUALIZATION FILES CREATED:")
    print("- enhanced_generator_overview.png")
    print("- scam_patterns_analysis.png")
    print("- legitimate_patterns_analysis.png")

if __name__ == "__main__":
    main()
