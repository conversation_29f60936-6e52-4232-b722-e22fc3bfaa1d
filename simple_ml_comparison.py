#!/usr/bin/env python3
"""
Simple ML Model Comparison: SVM vs Decision Trees vs Naive Bayes
Focus on core analysis without complex visualizations
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.naive_bayes import MultinomialNB, BernoulliNB
from sklearn.svm import SVC, LinearSVC
from sklearn.tree import DecisionTreeClassifier
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import classification_report, accuracy_score, precision_score, recall_score, f1_score
from sklearn.pipeline import Pipeline
import re
import time
import warnings
warnings.filterwarnings('ignore')

def load_and_prepare_data(dataset_path):
    """Load and prepare the dataset"""
    print("="*60)
    print("LOADING DATASET")
    print("="*60)
    
    df = pd.read_csv(dataset_path)
    print(f"Dataset: {df.shape[0]} rows, {df.shape[1]} columns")
    
    # Create binary classification
    scam_labels = ['ADVANCE_FEE_SCAM', 'ROMANCE_SCAM', 'INVESTMENT_SCAM', 'SCAM_SAMPLE']
    df['is_scam'] = df['Label'].apply(lambda x: 1 if x in scam_labels else 0)
    
    print(f"Scam: {df['is_scam'].sum()}, Legitimate: {(df['is_scam'] == 0).sum()}")
    
    # Clean text
    def clean_text(text):
        if pd.isna(text):
            return ""
        text = text.lower()
        text = re.sub(r'[^\w\s]', ' ', text)
        text = re.sub(r'\s+', ' ', text).strip()
        return text
    
    df['cleaned_text'] = df['Text Message / Email / Chat'].apply(clean_text)
    df = df[df['cleaned_text'].str.len() >= 10].reset_index(drop=True)
    
    return df

def train_and_compare_models(df):
    """Train and compare all models"""
    print("\n" + "="*60)
    print("MODEL COMPARISON")
    print("="*60)
    
    # Prepare data
    X = df['cleaned_text']
    y = df['is_scam']
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)
    
    # Define models
    models = {
        'Naive Bayes (Multinomial)': Pipeline([
            ('tfidf', TfidfVectorizer(max_features=3000, stop_words='english')),
            ('nb', MultinomialNB())
        ]),
        'Naive Bayes (Bernoulli)': Pipeline([
            ('tfidf', TfidfVectorizer(max_features=3000, stop_words='english', binary=True)),
            ('nb', BernoulliNB())
        ]),
        'SVM (Linear)': Pipeline([
            ('tfidf', TfidfVectorizer(max_features=3000, stop_words='english')),
            ('svm', LinearSVC(random_state=42, max_iter=2000))
        ]),
        'SVM (RBF)': Pipeline([
            ('tfidf', TfidfVectorizer(max_features=2000, stop_words='english')),
            ('svm', SVC(kernel='rbf', random_state=42))
        ]),
        'Decision Tree': Pipeline([
            ('tfidf', TfidfVectorizer(max_features=2000, stop_words='english')),
            ('dt', DecisionTreeClassifier(random_state=42, max_depth=10))
        ]),
        'Random Forest': Pipeline([
            ('tfidf', TfidfVectorizer(max_features=2000, stop_words='english')),
            ('rf', RandomForestClassifier(n_estimators=50, random_state=42, max_depth=8))
        ])
    }
    
    results = []
    
    for name, model in models.items():
        print(f"\nTraining {name}...")
        
        # Time training
        start_time = time.time()
        model.fit(X_train, y_train)
        training_time = time.time() - start_time
        
        # Make predictions
        start_time = time.time()
        y_pred = model.predict(X_test)
        prediction_time = time.time() - start_time
        
        # Calculate metrics
        accuracy = accuracy_score(y_test, y_pred)
        precision = precision_score(y_test, y_pred)
        recall = recall_score(y_test, y_pred)
        f1 = f1_score(y_test, y_pred)
        
        # Cross-validation
        cv_scores = cross_val_score(model, X_train, y_train, cv=5, scoring='accuracy')
        
        results.append({
            'Model': name,
            'Test_Accuracy': accuracy,
            'Precision': precision,
            'Recall': recall,
            'F1_Score': f1,
            'CV_Mean': cv_scores.mean(),
            'CV_Std': cv_scores.std(),
            'Training_Time': training_time,
            'Prediction_Time': prediction_time
        })
        
        print(f"  Accuracy: {accuracy:.4f}")
        print(f"  F1-Score: {f1:.4f}")
        print(f"  CV Score: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")
        print(f"  Time: {training_time:.3f}s train, {prediction_time:.3f}s predict")
    
    return pd.DataFrame(results)

def analyze_results(results_df):
    """Analyze and explain the results"""
    print("\n" + "="*60)
    print("DETAILED RESULTS ANALYSIS")
    print("="*60)
    
    # Sort by F1-score
    results_df = results_df.sort_values('F1_Score', ascending=False)
    
    print("\nModel Performance Summary:")
    print("-" * 80)
    for _, row in results_df.iterrows():
        print(f"{row['Model']:<25} | F1: {row['F1_Score']:.4f} | CV: {row['CV_Mean']:.4f} | Time: {row['Training_Time']:.3f}s")
    
    print("\n" + "="*60)
    print("WHY NAIVE BAYES EXCELS")
    print("="*60)
    
    print("\n1. MATHEMATICAL ADVANTAGES:")
    print("   • Probabilistic model naturally handles uncertainty")
    print("   • Bayes theorem provides optimal classification under independence assumption")
    print("   • Log-likelihood computation prevents numerical underflow")
    
    print("\n2. TEXT DATA CHARACTERISTICS:")
    print("   • High-dimensional sparse feature space (typical in text)")
    print("   • Word independence assumption reasonable for bag-of-words")
    print("   • Effective smoothing handles unseen word combinations")
    
    print("\n3. PRACTICAL ADVANTAGES:")
    print("   • Fast training: O(n) time complexity")
    print("   • Memory efficient: stores only word probabilities")
    print("   • No hyperparameter tuning required")
    print("   • Robust to irrelevant features")
    
    print("\n4. SCAM DETECTION SPECIFIC:")
    print("   • Scam keywords are often independent indicators")
    print("   • Works well with limited training data")
    print("   • Provides probability scores for confidence thresholding")
    print("   • Less prone to overfitting than complex models")
    
    print("\n" + "="*60)
    print("MODEL COMPARISON INSIGHTS")
    print("="*60)
    
    nb_models = results_df[results_df['Model'].str.contains('Naive Bayes')]
    svm_models = results_df[results_df['Model'].str.contains('SVM')]
    tree_models = results_df[results_df['Model'].str.contains('Tree|Forest')]
    
    print(f"\nNAIVE BAYES PERFORMANCE:")
    print(f"  Average F1-Score: {nb_models['F1_Score'].mean():.4f}")
    print(f"  Average CV Score: {nb_models['CV_Mean'].mean():.4f}")
    print(f"  Average Training Time: {nb_models['Training_Time'].mean():.3f}s")
    
    print(f"\nSVM PERFORMANCE:")
    print(f"  Average F1-Score: {svm_models['F1_Score'].mean():.4f}")
    print(f"  Average CV Score: {svm_models['CV_Mean'].mean():.4f}")
    print(f"  Average Training Time: {svm_models['Training_Time'].mean():.3f}s")
    
    print(f"\nTREE-BASED PERFORMANCE:")
    print(f"  Average F1-Score: {tree_models['F1_Score'].mean():.4f}")
    print(f"  Average CV Score: {tree_models['CV_Mean'].mean():.4f}")
    print(f"  Average Training Time: {tree_models['Training_Time'].mean():.3f}s")
    
    print("\n" + "="*60)
    print("BUSINESS RECOMMENDATIONS")
    print("="*60)
    
    best_model = results_df.iloc[0]
    print(f"\nBEST OVERALL MODEL: {best_model['Model']}")
    print(f"  F1-Score: {best_model['F1_Score']:.4f}")
    print(f"  Cross-Validation: {best_model['CV_Mean']:.4f}")
    print(f"  Training Time: {best_model['Training_Time']:.3f}s")
    
    print("\nDEPLOYMENT RECOMMENDATIONS:")
    print("1. PRODUCTION SYSTEM: Use Naive Bayes (Multinomial)")
    print("   • Fast training and prediction")
    print("   • Reliable performance")
    print("   • Easy to maintain and update")
    
    print("\n2. HIGH-ACCURACY NEEDS: Consider Linear SVM")
    print("   • Slightly better generalization")
    print("   • Good for batch processing")
    print("   • Requires more computational resources")
    
    print("\n3. INTERPRETABILITY: Use Decision Trees")
    print("   • Explainable decision paths")
    print("   • Good for regulatory compliance")
    print("   • May require ensemble methods for stability")

def main():
    """Main function"""
    # Find dataset
    import glob
    import os
    
    dataset_files = glob.glob("enhanced_dataset_*.csv")
    if not dataset_files:
        dataset_files = glob.glob("*dataset*.csv")
    
    if not dataset_files:
        print("No dataset found!")
        return
    
    latest_dataset = max(dataset_files, key=os.path.getctime)
    print(f"Using: {latest_dataset}")
    
    # Run analysis
    df = load_and_prepare_data(latest_dataset)
    results_df = train_and_compare_models(df)
    analyze_results(results_df)
    
    # Save results
    results_df.to_csv('ml_comparison_results.csv', index=False)
    print(f"\nResults saved to: ml_comparison_results.csv")

if __name__ == "__main__":
    main()
