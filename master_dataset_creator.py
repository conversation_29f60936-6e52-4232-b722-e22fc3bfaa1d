#!/usr/bin/env python3
"""
Master Dataset Creator for Nigerian Scam Detection
Combines web scraping and enhanced data generation to create 200+ row dataset
"""

import pandas as pd
from datetime import datetime
import logging
from scam_dataset_scraper import ScamD<PERSON><PERSON><PERSON>craper
from enhanced_dataset_generator import EnhancedDatasetGenerator

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MasterDatasetCreator:
    def __init__(self):
        self.all_data = []
        self.target_rows = 200
        
    def create_comprehensive_dataset(self):
        """Create comprehensive dataset with 200+ rows"""
        logger.info("Starting comprehensive dataset creation...")
        
        # Step 1: Generate enhanced sample data
        logger.info("Step 1: Generating enhanced sample data...")
        generator = EnhancedDatasetGenerator()
        enhanced_data = generator.generate_complete_dataset()
        self.all_data.extend(enhanced_data)
        logger.info(f"Added {len(enhanced_data)} enhanced records")
        
        # Step 2: Run web scraper
        logger.info("Step 2: Running web scraper...")
        scraper = ScamDatasetScraper()
        
        # Add comprehensive sample data from scraper
        scraper.add_comprehensive_sample_data()
        self.all_data.extend(scraper.data)
        logger.info(f"Added {len(scraper.data)} sample records from scraper")
        
        # Try to scrape websites (may not always work due to site restrictions)
        try:
            scraper.data = []  # Reset to avoid duplicates
            scraper.id_counter = len(self.all_data) + 1
            scraper.scrape_all_websites()
            self.all_data.extend(scraper.data)
            logger.info(f"Added {len(scraper.data)} scraped records")
        except Exception as e:
            logger.warning(f"Web scraping encountered issues: {e}")
            logger.info("Continuing with generated data...")
        
        # Step 3: Add more data if needed to reach target
        current_count = len(self.all_data)
        if current_count < self.target_rows:
            needed = self.target_rows - current_count
            logger.info(f"Step 3: Need {needed} more records to reach target of {self.target_rows}")
            
            # Generate additional data
            additional_generator = EnhancedDatasetGenerator()
            additional_generator.id_counter = current_count + 1
            
            # Generate more of each type proportionally
            additional_generator.generate_advance_fee_scams(max(10, needed // 4))
            additional_generator.generate_romance_scams(max(8, needed // 5))
            additional_generator.generate_investment_scams(max(10, needed // 4))
            additional_generator.generate_legitimate_messages(max(12, needed // 3))
            
            self.all_data.extend(additional_generator.data)
            logger.info(f"Added {len(additional_generator.data)} additional records")
        
        # Step 4: Clean and deduplicate data
        self.clean_dataset()
        
        logger.info(f"Final dataset contains {len(self.all_data)} records")
        return self.all_data
    
    def clean_dataset(self):
        """Clean and deduplicate the dataset"""
        logger.info("Cleaning and deduplicating dataset...")
        
        # Remove duplicates based on text content
        seen_texts = set()
        cleaned_data = []
        
        for i, record in enumerate(self.all_data):
            text = record['Text Message / Email / Chat'].strip().lower()
            if text not in seen_texts and len(text) > 20:  # Minimum length check
                seen_texts.add(text)
                # Update ID to be sequential
                record['ID'] = len(cleaned_data) + 1
                cleaned_data.append(record)
        
        original_count = len(self.all_data)
        self.all_data = cleaned_data
        logger.info(f"Removed {original_count - len(cleaned_data)} duplicates/short texts")
    
    def save_dataset(self, filename=None):
        """Save the final dataset"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"nigerian_scam_dataset_{timestamp}.csv"
        
        df = pd.DataFrame(self.all_data)
        df.to_csv(filename, index=False, encoding='utf-8')
        
        # Also save as JSON
        json_filename = filename.replace('.csv', '.json')
        df.to_json(json_filename, orient='records', indent=2)
        
        logger.info(f"Dataset saved as {filename} and {json_filename}")
        return filename
    
    def print_dataset_summary(self):
        """Print comprehensive dataset summary"""
        print(f"\n{'='*70}")
        print(f"NIGERIAN SCAM DETECTION DATASET - FINAL SUMMARY")
        print(f"{'='*70}")
        print(f"Total records: {len(self.all_data)}")
        print(f"Target achieved: {'✓ YES' if len(self.all_data) >= self.target_rows else '✗ NO'}")
        
        # Label distribution
        label_counts = {}
        for record in self.all_data:
            label = record['Label']
            label_counts[label] = label_counts.get(label, 0) + 1
        
        print(f"\nLabel Distribution:")
        for label, count in sorted(label_counts.items()):
            percentage = (count / len(self.all_data)) * 100
            print(f"  {label:<25}: {count:>3} records ({percentage:>5.1f}%)")
        
        # Sample records
        print(f"\nSample Records:")
        for i, record in enumerate(self.all_data[:3]):
            print(f"\n  Record {record['ID']}:")
            print(f"    Label: {record['Label']}")
            print(f"    Text: {record['Text Message / Email / Chat'][:80]}...")
        
        print(f"\n{'='*70}")
        print(f"Dataset is ready for scam detection model training!")
        print(f"{'='*70}")

def main():
    """Main function to create the comprehensive dataset"""
    creator = MasterDatasetCreator()
    
    try:
        # Create comprehensive dataset
        dataset = creator.create_comprehensive_dataset()
        
        # Save dataset
        filename = creator.save_dataset()
        
        # Print summary
        creator.print_dataset_summary()
        
        print(f"\n🎉 SUCCESS! Created Nigerian scam dataset with {len(dataset)} records")
        print(f"📁 Files created:")
        print(f"   - {filename}")
        print(f"   - {filename.replace('.csv', '.json')}")
        print(f"\n💡 You can now use this dataset for:")
        print(f"   - Training scam detection models")
        print(f"   - Text classification research")
        print(f"   - Fraud prevention systems")
        
    except Exception as e:
        logger.error(f"Error creating dataset: {e}")
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
