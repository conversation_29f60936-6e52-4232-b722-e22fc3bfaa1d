#!/usr/bin/env python3
"""
Simple SVM Analysis for Nigerian Scam Detection
Focus on core SVM concepts and practical implementation
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.svm import SVC, LinearSVC
from sklearn.metrics import classification_report, accuracy_score
from sklearn.pipeline import Pipeline
import re
import time

def load_and_prepare_data():
    """Load and prepare the scam dataset"""
    print("="*60)
    print("SVM SCAM DETECTION ANALYSIS")
    print("="*60)
    
    # Load data
    df = pd.read_csv('scam_dataset_20250704_180433.csv')
    print(f"Dataset: {df.shape[0]} rows, {df.shape[1]} columns")
    
    # Show label distribution
    print(f"\nLabel distribution:")
    for label, count in df['Label'].value_counts().items():
        print(f"  {label}: {count}")
    
    # Create binary classification
    scam_labels = ['SCAM_SAMPLE', 'SCAM_WARNING', 'SCAM_ALERT']
    df['is_scam'] = df['Label'].apply(lambda x: 1 if x in scam_labels else 0)
    
    print(f"\nBinary classification:")
    print(f"  Scam: {df['is_scam'].sum()}, Legitimate: {(df['is_scam'] == 0).sum()}")
    
    # Clean text
    def clean_text(text):
        if pd.isna(text):
            return ""
        text = text.lower()
        text = re.sub(r'[^\w\s]', ' ', text)
        text = re.sub(r'\s+', ' ', text).strip()
        return text
    
    df['cleaned_text'] = df['Text Message / Email / Chat'].apply(clean_text)
    df = df[df['cleaned_text'].str.len() >= 10].reset_index(drop=True)
    
    print(f"After preprocessing: {len(df)} messages")
    return df

def explain_svm_fundamentals():
    """Explain SVM fundamentals for scam detection"""
    print("\n" + "="*60)
    print("HOW SVM WORKS FOR SCAM DETECTION")
    print("="*60)
    
    print("🎯 CORE CONCEPT:")
    print("   SVM finds the optimal boundary (hyperplane) that best")
    print("   separates scam messages from legitimate messages")
    
    print("\n📊 IN TEXT CLASSIFICATION:")
    print("   • Each word becomes a dimension")
    print("   • TF-IDF scores are coordinates")
    print("   • SVM finds the best separating line/plane")
    
    print("\n🔍 EXAMPLE:")
    print("   Message: 'Send money urgently for lottery prize'")
    print("   Features: [money: 0.8, urgent: 0.6, lottery: 0.9, ...]")
    print("   SVM Decision: 0.8×w₁ + 0.6×w₂ + 0.9×w₃ + ... > threshold")
    print("   If result > 0: SCAM, else: LEGITIMATE")
    
    print("\n⚖️ MARGIN MAXIMIZATION:")
    print("   • SVM doesn't just find any separating line")
    print("   • It finds the line with maximum margin")
    print("   • This provides better generalization")
    print("   • More robust to new, unseen messages")

def train_svm_models(df):
    """Train different SVM models"""
    print("\n" + "="*60)
    print("TRAINING SVM MODELS")
    print("="*60)
    
    # Prepare data
    X = df['cleaned_text']
    y = df['is_scam']
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)
    
    print(f"Training: {len(X_train)}, Test: {len(X_test)}")
    
    # Define SVM models
    models = {
        'Linear SVM': Pipeline([
            ('tfidf', TfidfVectorizer(max_features=1000, stop_words='english')),
            ('svm', LinearSVC(C=1.0, random_state=42, max_iter=2000))
        ]),
        'RBF SVM': Pipeline([
            ('tfidf', TfidfVectorizer(max_features=800, stop_words='english')),
            ('svm', SVC(kernel='rbf', C=1.0, random_state=42, probability=True))
        ]),
        'Linear SVM (Optimized)': Pipeline([
            ('tfidf', TfidfVectorizer(max_features=1500, stop_words='english', ngram_range=(1, 2))),
            ('svm', LinearSVC(C=10.0, random_state=42, max_iter=2000))
        ])
    }
    
    results = []
    trained_models = {}
    
    for name, model in models.items():
        print(f"\n🔧 Training {name}...")
        
        # Train model
        start_time = time.time()
        model.fit(X_train, y_train)
        training_time = time.time() - start_time
        
        # Test model
        y_pred = model.predict(X_test)
        accuracy = accuracy_score(y_test, y_pred)
        
        # Cross-validation
        cv_scores = cross_val_score(model, X_train, y_train, cv=3)
        
        results.append({
            'Model': name,
            'Accuracy': accuracy,
            'CV_Mean': cv_scores.mean(),
            'CV_Std': cv_scores.std(),
            'Training_Time': training_time
        })
        
        trained_models[name] = model
        
        print(f"   Accuracy: {accuracy:.4f}")
        print(f"   CV Score: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")
        print(f"   Training Time: {training_time:.3f}s")
    
    return pd.DataFrame(results), trained_models, X_test, y_test

def analyze_linear_svm_weights(model):
    """Analyze Linear SVM feature weights"""
    print("\n" + "="*60)
    print("LINEAR SVM DECISION ANALYSIS")
    print("="*60)
    
    # Get components
    vectorizer = model.named_steps['tfidf']
    classifier = model.named_steps['svm']
    
    # Get feature weights
    feature_names = vectorizer.get_feature_names_out()
    weights = classifier.coef_[0]
    bias = classifier.intercept_[0]
    
    print(f"📊 SVM DECISION FUNCTION:")
    print(f"   f(x) = Σ(weight × feature) + bias")
    print(f"   Bias: {bias:.4f}")
    print(f"   Total features: {len(feature_names)}")
    print(f"   Classification: SCAM if f(x) > 0")
    
    # Sort features by weight
    feature_weights = list(zip(feature_names, weights))
    feature_weights.sort(key=lambda x: x[1], reverse=True)
    
    print(f"\n🚨 TOP SCAM INDICATORS (Positive weights):")
    scam_indicators = [fw for fw in feature_weights if fw[1] > 0][:8]
    for i, (feature, weight) in enumerate(scam_indicators, 1):
        print(f"   {i}. {feature:15} (+{weight:.4f})")
    
    print(f"\n✅ TOP LEGITIMATE INDICATORS (Negative weights):")
    legit_indicators = [fw for fw in feature_weights if fw[1] < 0][-8:]
    for i, (feature, weight) in enumerate(legit_indicators, 1):
        print(f"   {i}. {feature:15} ({weight:.4f})")

def test_svm_predictions(model):
    """Test SVM on sample messages"""
    print("\n" + "="*60)
    print("SVM PREDICTION EXAMPLES")
    print("="*60)
    
    test_messages = [
        ("Congratulations! You won $1,000,000 lottery. Send bank details.", "SCAM"),
        ("URGENT: Account will be closed. Verify now.", "SCAM"),
        ("Central Bank announces new exchange rate.", "LEGITIMATE"),
        ("EFCC warns against advance fee fraud.", "LEGITIMATE")
    ]
    
    for i, (message, expected) in enumerate(test_messages, 1):
        prediction = model.predict([message])[0]
        result = "SCAM" if prediction == 1 else "LEGITIMATE"
        
        # Get decision score
        decision_score = model.decision_function([message])[0]
        
        print(f"\n📧 Test {i}:")
        print(f"   Message: {message}")
        print(f"   Expected: {expected}")
        print(f"   SVM Prediction: {result}")
        print(f"   Decision Score: {decision_score:.4f}")
        print(f"   Status: {'✅ CORRECT' if result == expected else '❌ WRONG'}")

def explain_svm_advantages():
    """Explain SVM advantages and disadvantages"""
    print("\n" + "="*60)
    print("SVM ADVANTAGES & DISADVANTAGES")
    print("="*60)
    
    print("✅ ADVANTAGES:")
    print("   1. EFFECTIVE IN HIGH DIMENSIONS")
    print("      • Perfect for text with thousands of words")
    print("      • No curse of dimensionality")
    
    print("\n   2. MEMORY EFFICIENT")
    print("      • Only stores support vectors")
    print("      • Compact model representation")
    
    print("\n   3. VERSATILE KERNELS")
    print("      • Linear: Fast and interpretable")
    print("      • RBF: Captures complex patterns")
    print("      • Polynomial: Models interactions")
    
    print("\n   4. ROBUST TO OUTLIERS")
    print("      • Focus on boundary points")
    print("      • Good generalization")
    
    print("\n❌ DISADVANTAGES:")
    print("   1. NO PROBABILITY SCORES")
    print("      • Only binary decisions")
    print("      • Harder to set thresholds")
    
    print("\n   2. HYPERPARAMETER SENSITIVE")
    print("      • C parameter needs tuning")
    print("      • Kernel parameters matter")
    
    print("\n   3. TRAINING TIME")
    print("      • Slower than Naive Bayes")
    print("      • O(n²) to O(n³) complexity")
    
    print("\n   4. FEATURE SCALING REQUIRED")
    print("      • Sensitive to different scales")
    print("      • TF-IDF helps normalize")

def main():
    """Main analysis function"""
    # Load data
    df = load_and_prepare_data()
    
    # Explain SVM
    explain_svm_fundamentals()
    
    # Train models
    results_df, models, X_test, y_test = train_svm_models(df)
    
    # Analyze best model
    best_model_name = results_df.loc[results_df['CV_Mean'].idxmax(), 'Model']
    best_model = models[best_model_name]
    
    print(f"\n🏆 BEST MODEL: {best_model_name}")
    
    # Analyze Linear SVM if available
    if 'Linear' in best_model_name:
        analyze_linear_svm_weights(best_model)
    
    # Test predictions
    test_svm_predictions(best_model)
    
    # Explain advantages
    explain_svm_advantages()
    
    print("\n" + "="*60)
    print("CONCLUSION")
    print("="*60)
    print("SVM excels in scam detection because:")
    print("• Handles high-dimensional text data effectively")
    print("• Finds optimal decision boundary with maximum margin")
    print("• Robust generalization to new messages")
    print("• Linear SVM provides interpretable feature weights")
    print("• Strong theoretical foundation ensures reliable performance")
    
    # Save results
    results_df.to_csv('svm_results.csv', index=False)
    print(f"\nResults saved to: svm_results.csv")

if __name__ == "__main__":
    main()
