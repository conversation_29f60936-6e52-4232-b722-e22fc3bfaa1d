#!/usr/bin/env python3
"""
SVM Analysis with Comprehensive Visualizations
Nigerian Scam Detection Dataset Analysis
"""

import pandas as pd
import numpy as np
import json
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split, cross_val_score, validation_curve
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.svm import LinearSVC
from sklearn.metrics import classification_report, accuracy_score, confusion_matrix
from sklearn.metrics import precision_score, recall_score, f1_score, roc_curve, auc
from sklearn.pipeline import Pipeline
from sklearn.decomposition import PCA
import re
import warnings
warnings.filterwarnings('ignore')

# Set style for better plots
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

def load_and_prepare_data():
    """Load and prepare the JSON dataset"""
    print("="*60)
    print("SVM ANALYSIS WITH VISUALIZATIONS")
    print("Nigerian Scam Detection Dataset")
    print("="*60)
    
    # Load JSON data
    with open('nigerian_scam_dataset_20250704_183721.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    df = pd.DataFrame(data)
    print(f"Dataset: {df.shape[0]} rows, {df.shape[1]} columns")
    
    # Show label distribution
    print(f"\nLabel distribution:")
    label_counts = df['Label'].value_counts()
    for label, count in label_counts.items():
        print(f"  {label}: {count}")
    
    # Create binary classification
    scam_labels = ['ADVANCE_FEE_SCAM', 'LOTTERY_SCAM', 'ROMANCE_SCAM', 'INVESTMENT_SCAM', 
                  'PHISHING_SCAM', 'EMPLOYMENT_SCAM', 'CHARITY_SCAM', 'SCAM_SAMPLE', 
                  'SCAM_ALERT', 'SCAM_WARNING']
    df['is_scam'] = df['Label'].apply(lambda x: 1 if x in scam_labels else 0)
    
    print(f"\nBinary classification:")
    print(f"  Scam: {df['is_scam'].sum()}, Legitimate: {(df['is_scam'] == 0).sum()}")
    print(f"  Scam percentage: {df['is_scam'].mean():.1%}")
    
    # Clean text
    def clean_text(text):
        if pd.isna(text):
            return ""
        text = text.lower()
        text = re.sub(r'[^\w\s]', ' ', text)
        text = re.sub(r'\s+', ' ', text).strip()
        return text
    
    df['cleaned_text'] = df['Text Message / Email / Chat'].apply(clean_text)
    df = df[df['cleaned_text'].str.len() >= 10].reset_index(drop=True)
    
    return df

def create_dataset_visualization(df):
    """Create visualizations of the dataset"""
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('Nigerian Scam Dataset Analysis', fontsize=16, fontweight='bold')
    
    # 1. Label distribution
    label_counts = df['Label'].value_counts()
    axes[0, 0].pie(label_counts.values, labels=label_counts.index, autopct='%1.1f%%', startangle=90)
    axes[0, 0].set_title('Distribution of Message Types')
    
    # 2. Binary classification
    binary_counts = df['is_scam'].value_counts()
    binary_labels = ['Legitimate', 'Scam']
    colors = ['lightgreen', 'lightcoral']
    axes[0, 1].bar(binary_labels, binary_counts.values, color=colors)
    axes[0, 1].set_title('Scam vs Legitimate Messages')
    axes[0, 1].set_ylabel('Count')
    
    # Add count labels on bars
    for i, v in enumerate(binary_counts.values):
        axes[0, 1].text(i, v + 2, str(v), ha='center', fontweight='bold')
    
    # 3. Message length distribution
    df['message_length'] = df['cleaned_text'].str.len()
    axes[1, 0].hist(df[df['is_scam'] == 0]['message_length'], alpha=0.7, label='Legitimate', bins=20, color='lightgreen')
    axes[1, 0].hist(df[df['is_scam'] == 1]['message_length'], alpha=0.7, label='Scam', bins=20, color='lightcoral')
    axes[1, 0].set_title('Message Length Distribution')
    axes[1, 0].set_xlabel('Message Length (characters)')
    axes[1, 0].set_ylabel('Frequency')
    axes[1, 0].legend()
    
    # 4. Top scam types
    scam_types = df[df['is_scam'] == 1]['Label'].value_counts().head(8)
    axes[1, 1].barh(range(len(scam_types)), scam_types.values, color='lightcoral')
    axes[1, 1].set_yticks(range(len(scam_types)))
    axes[1, 1].set_yticklabels(scam_types.index, fontsize=8)
    axes[1, 1].set_title('Top Scam Types')
    axes[1, 1].set_xlabel('Count')
    
    plt.tight_layout()
    plt.savefig('dataset_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

def train_svm_models(df):
    """Train different SVM models and return results"""
    print("\n" + "="*60)
    print("TRAINING SVM MODELS")
    print("="*60)
    
    # Prepare data
    X = df['cleaned_text']
    y = df['is_scam']
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)
    
    # Define SVM models
    svm_models = {
        'SVM Linear (C=0.1)': Pipeline([
            ('tfidf', TfidfVectorizer(max_features=1000, stop_words='english')),
            ('svm', LinearSVC(C=0.1, random_state=42, max_iter=2000))
        ]),
        'SVM Linear (C=1.0)': Pipeline([
            ('tfidf', TfidfVectorizer(max_features=1500, stop_words='english')),
            ('svm', LinearSVC(C=1.0, random_state=42, max_iter=2000))
        ]),
        'SVM Linear (C=10.0)': Pipeline([
            ('tfidf', TfidfVectorizer(max_features=2000, stop_words='english')),
            ('svm', LinearSVC(C=10.0, random_state=42, max_iter=2000))
        ]),
        'SVM Optimized': Pipeline([
            ('tfidf', TfidfVectorizer(max_features=2000, stop_words='english', ngram_range=(1, 2))),
            ('svm', LinearSVC(C=10.0, random_state=42, max_iter=2000))
        ])
    }
    
    results = []
    trained_models = {}
    
    for name, model in svm_models.items():
        print(f"\nTraining {name}...")
        
        # Train model
        model.fit(X_train, y_train)
        
        # Make predictions
        y_pred = model.predict(X_test)
        y_pred_proba = model.decision_function(X_test)  # SVM decision scores
        
        # Calculate metrics
        accuracy = accuracy_score(y_test, y_pred)
        precision = precision_score(y_test, y_pred, zero_division=0)
        recall = recall_score(y_test, y_pred, zero_division=0)
        f1 = f1_score(y_test, y_pred, zero_division=0)
        
        # Cross-validation
        cv_scores = cross_val_score(model, X_train, y_train, cv=5)
        
        results.append({
            'Model': name,
            'Accuracy': accuracy,
            'Precision': precision,
            'Recall': recall,
            'F1_Score': f1,
            'CV_Mean': cv_scores.mean(),
            'CV_Std': cv_scores.std()
        })
        
        trained_models[name] = {
            'model': model,
            'y_test': y_test,
            'y_pred': y_pred,
            'y_scores': y_pred_proba
        }
        
        print(f"   Accuracy: {accuracy:.4f}")
        print(f"   CV Score: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")
    
    return pd.DataFrame(results), trained_models, X_train, y_train

def create_performance_visualizations(results_df, trained_models):
    """Create performance comparison visualizations"""
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('SVM Model Performance Comparison', fontsize=16, fontweight='bold')
    
    # 1. Cross-validation accuracy comparison
    models = results_df['Model']
    cv_means = results_df['CV_Mean']
    cv_stds = results_df['CV_Std']
    
    bars = axes[0, 0].bar(range(len(models)), cv_means, yerr=cv_stds, capsize=5, 
                         color=['skyblue', 'lightgreen', 'lightcoral', 'gold'])
    axes[0, 0].set_title('Cross-Validation Accuracy')
    axes[0, 0].set_ylabel('Accuracy')
    axes[0, 0].set_xticks(range(len(models)))
    axes[0, 0].set_xticklabels([m.replace('SVM Linear ', '') for m in models], rotation=45)
    
    # Add value labels on bars
    for i, (bar, mean, std) in enumerate(zip(bars, cv_means, cv_stds)):
        axes[0, 0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + std + 0.01,
                       f'{mean:.3f}', ha='center', fontweight='bold')
    
    # 2. Metrics comparison
    metrics = ['Accuracy', 'Precision', 'Recall', 'F1_Score']
    x = np.arange(len(models))
    width = 0.2
    
    for i, metric in enumerate(metrics):
        axes[0, 1].bar(x + i*width, results_df[metric], width, label=metric, alpha=0.8)
    
    axes[0, 1].set_title('Performance Metrics Comparison')
    axes[0, 1].set_ylabel('Score')
    axes[0, 1].set_xticks(x + width * 1.5)
    axes[0, 1].set_xticklabels([m.replace('SVM Linear ', '') for m in models], rotation=45)
    axes[0, 1].legend()
    axes[0, 1].set_ylim(0, 1.1)
    
    # 3. Confusion Matrix for best model
    best_model_name = results_df.loc[results_df['CV_Mean'].idxmax(), 'Model']
    best_model_data = trained_models[best_model_name]
    
    cm = confusion_matrix(best_model_data['y_test'], best_model_data['y_pred'])
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', ax=axes[1, 0],
                xticklabels=['Legitimate', 'Scam'], yticklabels=['Legitimate', 'Scam'])
    axes[1, 0].set_title(f'Confusion Matrix - {best_model_name}')
    axes[1, 0].set_ylabel('True Label')
    axes[1, 0].set_xlabel('Predicted Label')
    
    # 4. ROC Curve for best model
    y_test = best_model_data['y_test']
    y_scores = best_model_data['y_scores']
    
    fpr, tpr, _ = roc_curve(y_test, y_scores)
    roc_auc = auc(fpr, tpr)
    
    axes[1, 1].plot(fpr, tpr, color='darkorange', lw=2, label=f'ROC curve (AUC = {roc_auc:.3f})')
    axes[1, 1].plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--', label='Random')
    axes[1, 1].set_xlim([0.0, 1.0])
    axes[1, 1].set_ylim([0.0, 1.05])
    axes[1, 1].set_xlabel('False Positive Rate')
    axes[1, 1].set_ylabel('True Positive Rate')
    axes[1, 1].set_title(f'ROC Curve - {best_model_name}')
    axes[1, 1].legend(loc="lower right")
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('svm_performance_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return best_model_name

def create_hyperparameter_analysis(X_train, y_train):
    """Analyze SVM hyperparameter effects"""
    print("\n" + "="*60)
    print("SVM HYPERPARAMETER ANALYSIS")
    print("="*60)
    
    # Create pipeline for validation curve
    pipeline = Pipeline([
        ('tfidf', TfidfVectorizer(max_features=1500, stop_words='english')),
        ('svm', LinearSVC(random_state=42, max_iter=2000))
    ])
    
    # Test different C values
    C_values = [0.01, 0.1, 1.0, 10.0, 100.0]
    
    print("Testing C parameter values...")
    train_scores, val_scores = validation_curve(
        pipeline, X_train, y_train, param_name='svm__C', param_range=C_values,
        cv=5, scoring='accuracy', n_jobs=-1
    )
    
    # Create visualization
    fig, axes = plt.subplots(1, 2, figsize=(15, 6))
    fig.suptitle('SVM Hyperparameter Analysis', fontsize=16, fontweight='bold')
    
    # 1. Validation curve for C parameter
    train_mean = train_scores.mean(axis=1)
    train_std = train_scores.std(axis=1)
    val_mean = val_scores.mean(axis=1)
    val_std = val_scores.std(axis=1)
    
    axes[0].semilogx(C_values, train_mean, 'o-', color='blue', label='Training accuracy')
    axes[0].fill_between(C_values, train_mean - train_std, train_mean + train_std, alpha=0.1, color='blue')
    axes[0].semilogx(C_values, val_mean, 'o-', color='red', label='Validation accuracy')
    axes[0].fill_between(C_values, val_mean - val_std, val_mean + val_std, alpha=0.1, color='red')
    axes[0].set_xlabel('C Parameter')
    axes[0].set_ylabel('Accuracy')
    axes[0].set_title('Validation Curve for C Parameter')
    axes[0].legend()
    axes[0].grid(True, alpha=0.3)
    
    # 2. Feature importance (top coefficients)
    # Train best model to get feature weights
    best_C = C_values[np.argmax(val_mean)]
    best_model = Pipeline([
        ('tfidf', TfidfVectorizer(max_features=1500, stop_words='english')),
        ('svm', LinearSVC(C=best_C, random_state=42, max_iter=2000))
    ])
    best_model.fit(X_train, y_train)
    
    # Get feature names and coefficients
    feature_names = best_model.named_steps['tfidf'].get_feature_names_out()
    coefficients = best_model.named_steps['svm'].coef_[0]
    
    # Get top positive and negative coefficients
    top_positive_idx = np.argsort(coefficients)[-10:]
    top_negative_idx = np.argsort(coefficients)[:10]
    
    top_features = []
    top_coefs = []
    colors = []
    
    # Add negative coefficients (scam indicators)
    for idx in top_negative_idx:
        top_features.append(feature_names[idx])
        top_coefs.append(abs(coefficients[idx]))
        colors.append('lightcoral')
    
    # Add positive coefficients (legitimate indicators)
    for idx in reversed(top_positive_idx):
        top_features.append(feature_names[idx])
        top_coefs.append(coefficients[idx])
        colors.append('lightgreen')
    
    # Plot feature importance
    y_pos = np.arange(len(top_features))
    bars = axes[1].barh(y_pos, top_coefs, color=colors)
    axes[1].set_yticks(y_pos)
    axes[1].set_yticklabels(top_features)
    axes[1].set_xlabel('Coefficient Value')
    axes[1].set_title('Top SVM Feature Weights')
    axes[1].axvline(x=0, color='black', linestyle='-', alpha=0.3)
    
    # Add legend
    from matplotlib.patches import Patch
    legend_elements = [Patch(facecolor='lightcoral', label='Scam Indicators'),
                      Patch(facecolor='lightgreen', label='Legitimate Indicators')]
    axes[1].legend(handles=legend_elements)
    
    plt.tight_layout()
    plt.savefig('svm_hyperparameter_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"Best C parameter: {best_C}")
    print(f"Best validation accuracy: {val_mean.max():.4f}")

def create_feature_space_visualization(df, best_model_name, trained_models):
    """Create 2D visualization of feature space using PCA"""
    print("\n" + "="*60)
    print("SVM FEATURE SPACE VISUALIZATION")
    print("="*60)
    
    # Get the best model
    best_model = trained_models[best_model_name]['model']
    
    # Transform text to TF-IDF features
    X = df['cleaned_text']
    y = df['is_scam']
    
    # Get TF-IDF features
    tfidf_features = best_model.named_steps['tfidf'].transform(X)
    
    # Apply PCA for 2D visualization
    pca = PCA(n_components=2, random_state=42)
    X_pca = pca.fit_transform(tfidf_features.toarray())
    
    # Create visualization
    fig, axes = plt.subplots(1, 2, figsize=(15, 6))
    fig.suptitle('SVM Feature Space Visualization (PCA)', fontsize=16, fontweight='bold')
    
    # 1. Scatter plot of data points
    scatter = axes[0].scatter(X_pca[:, 0], X_pca[:, 1], c=y, cmap='RdYlBu', alpha=0.7)
    axes[0].set_xlabel(f'First Principal Component ({pca.explained_variance_ratio_[0]:.1%} variance)')
    axes[0].set_ylabel(f'Second Principal Component ({pca.explained_variance_ratio_[1]:.1%} variance)')
    axes[0].set_title('Data Points in 2D Feature Space')
    plt.colorbar(scatter, ax=axes[0], label='Class (0=Legitimate, 1=Scam)')
    
    # 2. Decision boundary visualization
    # Create a mesh to plot the decision boundary
    h = 0.1  # step size in the mesh
    x_min, x_max = X_pca[:, 0].min() - 1, X_pca[:, 0].max() + 1
    y_min, y_max = X_pca[:, 1].min() - 1, X_pca[:, 1].max() + 1
    xx, yy = np.meshgrid(np.arange(x_min, x_max, h), np.arange(y_min, y_max, h))
    
    # Train SVM on PCA-transformed data for visualization
    from sklearn.svm import SVC
    svm_2d = SVC(kernel='linear', C=1.0, random_state=42)
    svm_2d.fit(X_pca, y)
    
    # Predict on mesh points
    Z = svm_2d.predict(np.c_[xx.ravel(), yy.ravel()])
    Z = Z.reshape(xx.shape)
    
    # Plot decision boundary
    axes[1].contourf(xx, yy, Z, alpha=0.3, cmap='RdYlBu')
    scatter2 = axes[1].scatter(X_pca[:, 0], X_pca[:, 1], c=y, cmap='RdYlBu', alpha=0.7)
    axes[1].set_xlabel(f'First Principal Component ({pca.explained_variance_ratio_[0]:.1%} variance)')
    axes[1].set_ylabel(f'Second Principal Component ({pca.explained_variance_ratio_[1]:.1%} variance)')
    axes[1].set_title('SVM Decision Boundary')
    
    plt.tight_layout()
    plt.savefig('svm_feature_space_visualization.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"PCA explained variance: {pca.explained_variance_ratio_.sum():.1%}")

def main():
    """Main analysis function"""
    # Load and prepare data
    df = load_and_prepare_data()
    
    # Create dataset visualizations
    create_dataset_visualization(df)
    
    # Train SVM models
    results_df, trained_models, X_train, y_train = train_svm_models(df)
    
    # Create performance visualizations
    best_model_name = create_performance_visualizations(results_df, trained_models)
    
    # Hyperparameter analysis
    create_hyperparameter_analysis(X_train, y_train)
    
    # Feature space visualization
    create_feature_space_visualization(df, best_model_name, trained_models)
    
    # Print summary
    print("\n" + "="*60)
    print("SVM ANALYSIS SUMMARY")
    print("="*60)
    
    print("\nModel Performance Summary:")
    results_sorted = results_df.sort_values('CV_Mean', ascending=False)
    for _, row in results_sorted.iterrows():
        print(f"  {row['Model']:<25}: CV {row['CV_Mean']:.4f}, F1 {row['F1_Score']:.4f}")
    
    best_row = results_sorted.iloc[0]
    print(f"\n🏆 Best SVM Model: {best_row['Model']}")
    print(f"   Cross-validation accuracy: {best_row['CV_Mean']:.4f}")
    print(f"   F1-Score: {best_row['F1_Score']:.4f}")
    
    print(f"\n📊 Visualizations created:")
    print(f"   • dataset_analysis.png - Dataset overview")
    print(f"   • svm_performance_analysis.png - Model performance comparison")
    print(f"   • svm_hyperparameter_analysis.png - Hyperparameter tuning")
    print(f"   • svm_feature_space_visualization.png - 2D feature space")
    
    # Save results
    results_df.to_csv('svm_analysis_results.csv', index=False)
    print(f"\n💾 Results saved to: svm_analysis_results.csv")

if __name__ == "__main__":
    main()
