# Scam Dataset Scraper

A Python web scraping tool to extract text data from Nigerian financial and scam-related websites for dataset creation.

## Features

- Scrapes multiple Nigerian financial and scam-related websites
- Uses both BeautifulSoup + Requests (for static content) and Selenium (for dynamic content)
- Outputs data in the format: ID, Text Message/Email/Chat, Label
- Handles rate limiting and respectful scraping practices
- Saves data in both CSV and JSON formats

## Target Websites & Sources

### Government & NGO Sources
1. **NCCC Reports** (https://nccc.npf.gov.ng/ereport/signin) - Police cybercrime reports
2. **NCCC News** (https://nccc.npf.gov.ng/news) - Cybercrime alerts and news
3. **EFCC Alerts** (https://www.efcc.gov.ng/efcc/records/efcc-alert) - Anti-corruption alerts
4. **EFCC Complaint Channels** (https://www.efcc.gov.ng/efcc/channels-of-reporting-complaints-2) - Reporting procedures
5. **ICPC Portal** (https://icpc.gov.ng/) - Anti-corruption commission
6. **FCCPC Consumer Protection** (https://fccpc.gov.ng/) - Consumer protection alerts
7. **SEC Investment Scam Alerts** (https://sec.gov.ng/category/investment-scam/) - Investment fraud warnings

### Financial Institutions
8. **CBN Financial Data** (https://www.cbn.gov.ng/rates/FinancialData.html) - Legitimate financial content
9. **CBN Fraud Awareness** (https://www.cbn.gov.ng/supervision/cpdfraudandscam.html) - Fraud prevention
10. **CBN Consumer Protection** (https://www.cbn.gov.ng/contacts/) - Banking guidelines

### Additional Sources
11. **ScamWatch Nigeria** (https://scamwatch.ng/scam/investment/investment-scam) - Scam warnings

## Installation

1. Install Python dependencies:
```bash
pip install -r requirements.txt
```

2. Install ChromeDriver for Selenium:
   - Download from: https://chromedriver.chromium.org/
   - Add to your system PATH
   - Or use: `pip install webdriver-manager` for automatic management

## Usage

### Option 1: Complete Dataset Creation (Recommended)
Creates 200+ rows combining web scraping and generated data:
```bash
python master_dataset_creator.py
```

### Option 2: Web Scraping Only
```bash
python scam_dataset_scraper.py
```

### Option 3: Enhanced Data Generation Only
```bash
python enhanced_dataset_generator.py
```

### Option 4: Test the System
```bash
python test_scraper.py
```

The scripts will:
1. Scrape Nigerian government and NGO websites
2. Generate realistic Nigerian scam patterns
3. Create legitimate financial content samples
4. Label all data appropriately
5. Save results to timestamped CSV and JSON files

## Output Format

| ID | Text Message / Email / Chat | Label |
|----|----------------------------|-------|
| 1  | "Investment opportunity..."  | SCAM_WARNING |
| 2  | "CBN exchange rate is..."   | LEGITIMATE_FINANCIAL |

## Labels Used

### Scam Categories
- `SCAM_REPORT`: Reports from NCCC
- `SCAM_ALERT`: Alerts from EFCC and other agencies
- `SCAM_WARNING`: Warnings from ScamWatch and government sites
- `ADVANCE_FEE_SCAM`: Nigerian 419 advance fee fraud
- `ROMANCE_SCAM`: Dating and relationship fraud
- `INVESTMENT_SCAM`: Fake investment opportunities
- `SCAM_SAMPLE`: Generated scam examples

### Legitimate Categories
- `LEGITIMATE_FINANCIAL`: Content from CBN and banks
- `LEGITIMATE_SAMPLE`: Generated legitimate examples
- `OFFICIAL_ALERT`: Government warnings and advisories
- `CORRUPTION_ALERT`: Anti-corruption information
- `CONSUMER_PROTECTION`: Consumer protection guidance
- `FRAUD_AWARENESS`: Educational fraud prevention content
- `COMPLAINT_PROCEDURE`: Official reporting procedures

## Configuration

You can modify the `websites` dictionary in the script to:
- Add new websites
- Change labels
- Switch between scraping methods (requests vs selenium)

## Notes

- The script includes delays between requests to be respectful to servers
- Some websites may require additional handling for login or dynamic content
- Always check robots.txt and terms of service before scraping
- Consider using proxies for large-scale scraping

## Troubleshooting

1. **ChromeDriver issues**: Ensure ChromeDriver is installed and in PATH
2. **Access denied**: Some sites may block automated requests
3. **Dynamic content not loading**: Increase wait times in Selenium configuration
