#!/usr/bin/env python3
"""
Display Sample Data Generated by Enhanced Dataset Generator
Shows examples of each type of data generated
"""

from enhanced_dataset_generator import EnhancedDatasetGenerator
import pandas as pd

def show_generator_samples():
    """Display sample data from each generation method"""
    print("="*80)
    print("ENHANCED DATASET GENERATOR - SAMPLE DATA SHOWCASE")
    print("="*80)
    
    generator = EnhancedDatasetGenerator()
    
    # Generate samples of each type
    print("\n🔴 ADVANCE FEE SCAM SAMPLES:")
    print("-" * 50)
    generator.generate_advance_fee_scams(5)
    for i, record in enumerate(generator.data[-5:], 1):
        print(f"\n{i}. {record['Text Message / Email / Chat']}")
    
    print("\n\n💕 ROMANCE SCAM SAMPLES:")
    print("-" * 50)
    generator.generate_romance_scams(3)
    for i, record in enumerate(generator.data[-3:], 1):
        print(f"\n{i}. {record['Text Message / Email / Chat']}")
    
    print("\n\n💰 INVESTMENT SCAM SAMPLES:")
    print("-" * 50)
    generator.generate_investment_scams(3)
    for i, record in enumerate(generator.data[-3:], 1):
        print(f"\n{i}. {record['Text Message / Email / Chat']}")
    
    print("\n\n✅ LEGITIMATE MESSAGE SAMPLES:")
    print("-" * 50)
    generator.generate_legitimate_messages(3)
    for i, record in enumerate(generator.data[-3:], 1):
        print(f"\n{i}. {record['Text Message / Email / Chat']}")
    
    # Show statistics
    df = pd.DataFrame(generator.data)
    
    print("\n\n📊 GENERATION STATISTICS:")
    print("="*50)
    print(f"Total records generated: {len(df)}")
    print(f"\nLabel distribution:")
    label_counts = df['Label'].value_counts()
    for label, count in label_counts.items():
        print(f"  {label}: {count} records")
    
    # Text length analysis
    df['text_length'] = df['Text Message / Email / Chat'].str.len()
    df['word_count'] = df['Text Message / Email / Chat'].str.split().str.len()
    
    print(f"\nText statistics:")
    print(f"  Average text length: {df['text_length'].mean():.1f} characters")
    print(f"  Average word count: {df['word_count'].mean():.1f} words")
    print(f"  Shortest message: {df['text_length'].min()} characters")
    print(f"  Longest message: {df['text_length'].max()} characters")
    
    # Show patterns detected
    print(f"\n🔍 PATTERNS DETECTED:")
    print("="*50)
    
    all_text = ' '.join(df['Text Message / Email / Chat']).lower()
    
    # Nigerian elements
    nigerian_elements = {
        'Nigerian cities': ['lagos', 'abuja', 'kano', 'ibadan', 'port harcourt'],
        'Nigerian banks': ['first bank', 'gtbank', 'access bank', 'zenith bank', 'uba'],
        'Government agencies': ['efcc', 'icpc', 'cbn', 'sec', 'ndic'],
        'Currency mentions': ['naira', '₦', '$', 'million', 'thousand']
    }
    
    for category, items in nigerian_elements.items():
        found_items = [item for item in items if item in all_text]
        if found_items:
            print(f"\n{category}:")
            for item in found_items:
                count = all_text.count(item)
                print(f"  - {item}: {count} mentions")
    
    print(f"\n🎯 GENERATOR FEATURES:")
    print("="*50)
    print("✓ Realistic Nigerian names and locations")
    print("✓ Authentic banking and financial terminology")
    print("✓ Variable amounts and percentages")
    print("✓ Multiple scam patterns (419, romance, investment)")
    print("✓ Legitimate government and financial content")
    print("✓ Proper Nigerian context and cultural elements")
    
    return df

def analyze_generator_effectiveness():
    """Analyze how effective the generator is at creating diverse content"""
    print(f"\n\n🔬 GENERATOR EFFECTIVENESS ANALYSIS:")
    print("="*60)
    
    generator = EnhancedDatasetGenerator()
    
    # Generate larger sample
    generator.generate_advance_fee_scams(20)
    generator.generate_romance_scams(15)
    generator.generate_investment_scams(15)
    generator.generate_legitimate_messages(20)
    
    df = pd.DataFrame(generator.data)
    
    # Diversity analysis
    unique_texts = df['Text Message / Email / Chat'].nunique()
    total_texts = len(df)
    diversity_ratio = unique_texts / total_texts
    
    print(f"Diversity Analysis:")
    print(f"  Total messages: {total_texts}")
    print(f"  Unique messages: {unique_texts}")
    print(f"  Diversity ratio: {diversity_ratio:.2%}")
    
    # Length variation
    df['text_length'] = df['Text Message / Email / Chat'].str.len()
    length_std = df['text_length'].std()
    length_mean = df['text_length'].mean()
    length_cv = length_std / length_mean
    
    print(f"\nLength Variation:")
    print(f"  Mean length: {length_mean:.1f} characters")
    print(f"  Standard deviation: {length_std:.1f}")
    print(f"  Coefficient of variation: {length_cv:.2%}")
    
    # Content variation by category
    print(f"\nContent Variation by Category:")
    for label in df['Label'].unique():
        subset = df[df['Label'] == label]
        unique_in_category = subset['Text Message / Email / Chat'].nunique()
        total_in_category = len(subset)
        category_diversity = unique_in_category / total_in_category
        print(f"  {label}: {category_diversity:.2%} unique")
    
    print(f"\n✅ QUALITY ASSESSMENT:")
    if diversity_ratio > 0.95:
        print("🟢 EXCELLENT: Very high content diversity")
    elif diversity_ratio > 0.90:
        print("🟡 GOOD: High content diversity")
    else:
        print("🔴 NEEDS IMPROVEMENT: Low content diversity")
    
    if length_cv > 0.3:
        print("🟢 EXCELLENT: Good length variation")
    elif length_cv > 0.2:
        print("🟡 GOOD: Moderate length variation")
    else:
        print("🔴 NEEDS IMPROVEMENT: Low length variation")

def main():
    """Main function to showcase the enhanced generator"""
    df = show_generator_samples()
    analyze_generator_effectiveness()
    
    print(f"\n\n🎉 ENHANCED DATASET GENERATOR SHOWCASE COMPLETE!")
    print("="*60)
    print("The enhanced generator successfully creates:")
    print("✓ Diverse Nigerian scam patterns")
    print("✓ Realistic financial content")
    print("✓ Authentic cultural context")
    print("✓ Variable message lengths and styles")
    print("✓ Multiple fraud categories")
    
    print(f"\n📁 Generated visualization files:")
    print("- enhanced_generator_overview.png")
    print("- scam_patterns_analysis.png") 
    print("- legitimate_patterns_analysis.png")

if __name__ == "__main__":
    main()
