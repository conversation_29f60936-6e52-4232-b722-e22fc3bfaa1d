#!/usr/bin/env python3
"""
Interactive Scam Predictor
Allows you to test the trained model with new messages
"""

import pandas as pd
import pickle
import os
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.naive_bayes import MultinomialNB
from sklearn.pipeline import Pipeline
from sklearn.model_selection import train_test_split
import re

class ScamPredictor:
    def __init__(self):
        self.model = None
        self.is_trained = False
        
    def train_model(self, dataset_path):
        """Train the model with the dataset"""
        print("Training scam detection model...")
        
        # Load data
        df = pd.read_csv(dataset_path)
        
        # Create binary classification
        scam_labels = ['ADVANCE_FEE_SCAM', 'ROMANCE_SCAM', 'INVESTMENT_SCAM', 'SCAM_SAMPLE', 'SCAM_ALERT', 'SCAM_WARNING']
        df['is_scam'] = df['Label'].apply(lambda x: 1 if x in scam_labels else 0)
        
        # Clean text
        def clean_text(text):
            if pd.isna(text):
                return ""
            text = text.lower()
            text = re.sub(r'http\S+|www\S+|https\S+', '', text, flags=re.MULTILINE)
            text = re.sub(r'\S+@\S+', '', text)
            text = re.sub(r'\+?\d[\d\s\-\(\)]{7,}\d', '', text)
            text = re.sub(r'\s+', ' ', text).strip()
            return text
        
        df['cleaned_text'] = df['Text Message / Email / Chat'].apply(clean_text)
        
        # Prepare features
        X = df['cleaned_text']
        y = df['is_scam']
        
        # Train the best model (Naive Bayes with TF-IDF)
        self.model = Pipeline([
            ('tfidf', TfidfVectorizer(max_features=5000, stop_words='english', ngram_range=(1, 2))),
            ('nb', MultinomialNB())
        ])
        
        self.model.fit(X, y)
        self.is_trained = True
        
        print("Model trained successfully!")
        return self.model
    
    def predict_message(self, message):
        """Predict if a message is scam or legitimate"""
        if not self.is_trained:
            raise ValueError("Model not trained yet!")
        
        # Clean the message
        cleaned_message = self.clean_text(message)
        
        # Make prediction
        prediction = self.model.predict([cleaned_message])[0]
        probability = self.model.predict_proba([cleaned_message])[0]
        
        result = "SCAM" if prediction == 1 else "LEGITIMATE"
        confidence = max(probability)
        
        return {
            'prediction': result,
            'confidence': confidence,
            'scam_probability': probability[1],
            'legitimate_probability': probability[0]
        }
    
    def clean_text(self, text):
        """Clean text for prediction"""
        if pd.isna(text):
            return ""
        text = str(text).lower()
        text = re.sub(r'http\S+|www\S+|https\S+', '', text, flags=re.MULTILINE)
        text = re.sub(r'\S+@\S+', '', text)
        text = re.sub(r'\+?\d[\d\s\-\(\)]{7,}\d', '', text)
        text = re.sub(r'\s+', ' ', text).strip()
        return text
    
    def save_model(self, filename="scam_detector_model.pkl"):
        """Save the trained model"""
        if not self.is_trained:
            raise ValueError("Model not trained yet!")
        
        with open(filename, 'wb') as f:
            pickle.dump(self.model, f)
        print(f"Model saved as {filename}")
    
    def load_model(self, filename="scam_detector_model.pkl"):
        """Load a pre-trained model"""
        if os.path.exists(filename):
            with open(filename, 'rb') as f:
                self.model = pickle.load(f)
            self.is_trained = True
            print(f"Model loaded from {filename}")
            return True
        else:
            print(f"Model file {filename} not found")
            return False

def interactive_mode():
    """Run interactive prediction mode"""
    predictor = ScamPredictor()
    
    # Try to load existing model or train new one
    if not predictor.load_model():
        # Find dataset and train model
        import glob
        dataset_files = glob.glob("nigerian_scam_dataset_*.csv")
        if not dataset_files:
            dataset_files = glob.glob("scam_dataset_*.csv")
        
        if not dataset_files:
            print("No dataset file found! Please run the scraper first.")
            return
        
        latest_dataset = max(dataset_files, key=os.path.getctime)
        predictor.train_model(latest_dataset)
        predictor.save_model()
    
    print("\n" + "="*60)
    print("NIGERIAN SCAM DETECTOR - INTERACTIVE MODE")
    print("="*60)
    print("Enter messages to check if they are scams or legitimate.")
    print("Type 'quit' to exit, 'examples' to see test examples.")
    print("="*60)
    
    while True:
        try:
            message = input("\nEnter message to analyze: ").strip()
            
            if message.lower() == 'quit':
                print("Goodbye!")
                break
            
            if message.lower() == 'examples':
                show_examples(predictor)
                continue
            
            if not message:
                print("Please enter a message.")
                continue
            
            # Make prediction
            result = predictor.predict_message(message)
            
            print(f"\n{'='*50}")
            print(f"PREDICTION RESULT")
            print(f"{'='*50}")
            print(f"Message: {message}")
            print(f"Prediction: {result['prediction']}")
            print(f"Confidence: {result['confidence']:.1%}")
            print(f"Scam Probability: {result['scam_probability']:.1%}")
            print(f"Legitimate Probability: {result['legitimate_probability']:.1%}")
            
            # Risk assessment
            if result['scam_probability'] > 0.8:
                risk = "🔴 HIGH RISK"
            elif result['scam_probability'] > 0.6:
                risk = "🟡 MEDIUM RISK"
            elif result['scam_probability'] > 0.4:
                risk = "🟠 LOW RISK"
            else:
                risk = "🟢 SAFE"
            
            print(f"Risk Level: {risk}")
            print(f"{'='*50}")
            
        except KeyboardInterrupt:
            print("\nGoodbye!")
            break
        except Exception as e:
            print(f"Error: {e}")

def show_examples(predictor):
    """Show example predictions"""
    examples = [
        "Congratulations! You have won $1,000,000 in the Nigerian National Lottery. Send your bank details to claim your prize.",
        "Central Bank of Nigeria announces new monetary policy rate of 18.75% effective immediately.",
        "URGENT: Your account will be closed unless you verify your details immediately. Click this link and enter your password.",
        "EFCC warns citizens against advance fee fraud. Always verify before making payments.",
        "Investment opportunity: Double your money in 30 days with our guaranteed forex trading system. Minimum investment ₦50,000.",
        "Nigerian Stock Exchange recorded a 2.3% gain in market capitalization this week.",
        "Hello my love, I am Sarah from UK. I want to transfer $2.5 million to your account. Reply with your bank details.",
        "Banks are required to maintain a minimum capital adequacy ratio of 15% according to CBN guidelines."
    ]
    
    print(f"\n{'='*60}")
    print("EXAMPLE PREDICTIONS")
    print("="*60)
    
    for i, example in enumerate(examples, 1):
        result = predictor.predict_message(example)
        print(f"\nExample {i}:")
        print(f"Message: {example[:80]}...")
        print(f"Prediction: {result['prediction']} ({result['confidence']:.1%} confidence)")

def batch_mode():
    """Run batch prediction mode"""
    predictor = ScamPredictor()
    
    # Load or train model
    if not predictor.load_model():
        import glob
        dataset_files = glob.glob("nigerian_scam_dataset_*.csv")
        if not dataset_files:
            dataset_files = glob.glob("scam_dataset_*.csv")
        
        if not dataset_files:
            print("No dataset file found!")
            return
        
        latest_dataset = max(dataset_files, key=os.path.getctime)
        predictor.train_model(latest_dataset)
        predictor.save_model()
    
    # Test with predefined messages
    test_messages = [
        "Congratulations! You have won $1,000,000 in the lottery. Send your bank details to claim.",
        "Central Bank of Nigeria announces new exchange rate of 461.50 NGN per USD.",
        "URGENT: Your account will be suspended. Click here to verify your details immediately.",
        "EFCC warns citizens against advance fee fraud. Always verify before making payments.",
        "Investment opportunity: Double your money in 30 days with guaranteed returns.",
        "Nigerian Stock Exchange recorded 2.3% gain in market capitalization this week.",
        "Hello, I am Mrs. Johnson from UK. I want to transfer $2.5 million to your account.",
        "Banks must maintain minimum capital adequacy ratio according to CBN guidelines."
    ]
    
    print("\n" + "="*60)
    print("BATCH PREDICTION RESULTS")
    print("="*60)
    
    for i, message in enumerate(test_messages, 1):
        result = predictor.predict_message(message)
        print(f"\nTest {i}:")
        print(f"Message: {message}")
        print(f"Prediction: {result['prediction']} (Confidence: {result['confidence']:.1%})")

def main():
    """Main function"""
    print("Nigerian Scam Detection Model")
    print("="*40)
    print("1. Interactive Mode - Test individual messages")
    print("2. Batch Mode - Test predefined examples")
    print("3. Examples - Show sample predictions")
    
    try:
        choice = input("\nSelect mode (1/2/3) or press Enter for interactive: ").strip()
        
        if choice == '2':
            batch_mode()
        elif choice == '3':
            predictor = ScamPredictor()
            if not predictor.load_model():
                import glob
                dataset_files = glob.glob("nigerian_scam_dataset_*.csv")
                if dataset_files:
                    latest_dataset = max(dataset_files, key=os.path.getctime)
                    predictor.train_model(latest_dataset)
            show_examples(predictor)
        else:
            interactive_mode()
            
    except KeyboardInterrupt:
        print("\nGoodbye!")

if __name__ == "__main__":
    main()
