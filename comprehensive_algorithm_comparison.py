#!/usr/bin/env python3
"""
Comprehensive comparison of SVM, Naive Bayes, and Decision Trees
Using the scam_dataset_20250704_180433.csv dataset
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.naive_bayes import MultinomialNB
from sklearn.svm import LinearSVC
from sklearn.tree import DecisionTreeClassifier
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import classification_report, accuracy_score, precision_score, recall_score, f1_score
from sklearn.pipeline import Pipeline
import re
import time

def load_and_prepare_data():
    """Load and prepare the dataset"""
    print("="*70)
    print("COMPREHENSIVE ML ALGORITHM COMPARISON")
    print("SVM vs Naive Bayes vs Decision Trees")
    print("="*70)
    
    df = pd.read_csv('scam_dataset_20250704_180433.csv')
    print(f"Dataset: {df.shape[0]} rows, {df.shape[1]} columns")
    
    # Create binary classification
    scam_labels = ['SCAM_SAMPLE', 'SCAM_WARNING', 'SCAM_ALERT']
    df['is_scam'] = df['Label'].apply(lambda x: 1 if x in scam_labels else 0)
    
    print(f"Scam: {df['is_scam'].sum()}, Legitimate: {(df['is_scam'] == 0).sum()}")
    
    # Clean text
    def clean_text(text):
        if pd.isna(text):
            return ""
        text = text.lower()
        text = re.sub(r'[^\w\s]', ' ', text)
        text = re.sub(r'\s+', ' ', text).strip()
        return text
    
    df['cleaned_text'] = df['Text Message / Email / Chat'].apply(clean_text)
    df = df[df['cleaned_text'].str.len() >= 10].reset_index(drop=True)
    
    return df

def train_all_algorithms(df):
    """Train all three algorithm types"""
    print("\n" + "="*70)
    print("TRAINING ALL ALGORITHMS")
    print("="*70)
    
    # Prepare data
    X = df['cleaned_text']
    y = df['is_scam']
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)
    
    # Define all models
    models = {
        # Naive Bayes variants
        'Naive Bayes (Multinomial)': Pipeline([
            ('tfidf', TfidfVectorizer(max_features=1000, stop_words='english')),
            ('nb', MultinomialNB())
        ]),
        
        # SVM variants
        'SVM (Linear)': Pipeline([
            ('tfidf', TfidfVectorizer(max_features=1000, stop_words='english')),
            ('svm', LinearSVC(C=1.0, random_state=42, max_iter=2000))
        ]),
        'SVM (Optimized)': Pipeline([
            ('tfidf', TfidfVectorizer(max_features=1500, stop_words='english', ngram_range=(1, 2))),
            ('svm', LinearSVC(C=10.0, random_state=42, max_iter=2000))
        ]),
        
        # Tree-based variants
        'Decision Tree': Pipeline([
            ('tfidf', TfidfVectorizer(max_features=800, stop_words='english')),
            ('dt', DecisionTreeClassifier(random_state=42, max_depth=10))
        ]),
        'Random Forest': Pipeline([
            ('tfidf', TfidfVectorizer(max_features=800, stop_words='english')),
            ('rf', RandomForestClassifier(n_estimators=50, random_state=42, max_depth=8))
        ])
    }
    
    results = []
    
    for name, model in models.items():
        print(f"\n🔧 Training {name}...")
        
        # Time training
        start_time = time.time()
        model.fit(X_train, y_train)
        training_time = time.time() - start_time
        
        # Make predictions
        start_time = time.time()
        y_pred = model.predict(X_test)
        prediction_time = time.time() - start_time
        
        # Calculate metrics
        accuracy = accuracy_score(y_test, y_pred)
        precision = precision_score(y_test, y_pred, zero_division=0)
        recall = recall_score(y_test, y_pred, zero_division=0)
        f1 = f1_score(y_test, y_pred, zero_division=0)
        
        # Cross-validation
        cv_scores = cross_val_score(model, X_train, y_train, cv=3, scoring='accuracy')
        
        results.append({
            'Algorithm': name.split('(')[0].strip(),
            'Variant': name,
            'Test_Accuracy': accuracy,
            'Precision': precision,
            'Recall': recall,
            'F1_Score': f1,
            'CV_Mean': cv_scores.mean(),
            'CV_Std': cv_scores.std(),
            'Training_Time': training_time,
            'Prediction_Time': prediction_time
        })
        
        print(f"   Accuracy: {accuracy:.4f}")
        print(f"   F1-Score: {f1:.4f}")
        print(f"   CV Score: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")
        print(f"   Time: {training_time:.3f}s train, {prediction_time:.3f}s predict")
    
    return pd.DataFrame(results)

def analyze_algorithm_strengths(results_df):
    """Analyze strengths of each algorithm type"""
    print("\n" + "="*70)
    print("ALGORITHM COMPARISON ANALYSIS")
    print("="*70)
    
    # Group by algorithm type
    nb_results = results_df[results_df['Algorithm'] == 'Naive Bayes']
    svm_results = results_df[results_df['Algorithm'] == 'SVM']
    tree_results = results_df[results_df['Algorithm'].isin(['Decision Tree', 'Random Forest'])]
    
    print("\n📊 PERFORMANCE SUMMARY:")
    print("-" * 50)
    
    print(f"NAIVE BAYES:")
    if not nb_results.empty:
        print(f"   Best CV Accuracy: {nb_results['CV_Mean'].max():.4f}")
        print(f"   Average Training Time: {nb_results['Training_Time'].mean():.3f}s")
        print(f"   Average F1-Score: {nb_results['F1_Score'].mean():.4f}")
    
    print(f"\nSUPPORT VECTOR MACHINE:")
    if not svm_results.empty:
        print(f"   Best CV Accuracy: {svm_results['CV_Mean'].max():.4f}")
        print(f"   Average Training Time: {svm_results['Training_Time'].mean():.3f}s")
        print(f"   Average F1-Score: {svm_results['F1_Score'].mean():.4f}")
    
    print(f"\nTREE-BASED METHODS:")
    if not tree_results.empty:
        print(f"   Best CV Accuracy: {tree_results['CV_Mean'].max():.4f}")
        print(f"   Average Training Time: {tree_results['Training_Time'].mean():.3f}s")
        print(f"   Average F1-Score: {tree_results['F1_Score'].mean():.4f}")

def explain_algorithm_characteristics():
    """Explain characteristics of each algorithm"""
    print("\n" + "="*70)
    print("ALGORITHM CHARACTERISTICS FOR SCAM DETECTION")
    print("="*70)
    
    print("\n🧠 NAIVE BAYES:")
    print("   ✅ STRENGTHS:")
    print("      • Fast training and prediction")
    print("      • Excellent with sparse text data")
    print("      • Probabilistic output for confidence")
    print("      • Works well with small datasets")
    print("      • Minimal hyperparameter tuning")
    
    print("   ❌ WEAKNESSES:")
    print("      • Independence assumption may not hold")
    print("      • Can be outperformed by complex models")
    print("      • Limited feature interaction modeling")
    
    print("\n⚖️ SUPPORT VECTOR MACHINE:")
    print("   ✅ STRENGTHS:")
    print("      • Excellent in high-dimensional spaces")
    print("      • Memory efficient (stores support vectors)")
    print("      • Robust to overfitting")
    print("      • Strong theoretical foundation")
    print("      • Versatile kernel options")
    
    print("   ❌ WEAKNESSES:")
    print("      • No probability estimates (by default)")
    print("      • Sensitive to feature scaling")
    print("      • Hyperparameter tuning required")
    print("      • Slower training on large datasets")
    
    print("\n🌳 DECISION TREES:")
    print("   ✅ STRENGTHS:")
    print("      • Highly interpretable")
    print("      • Handles non-linear relationships")
    print("      • No feature scaling required")
    print("      • Can model feature interactions")
    print("      • Provides feature importance")
    
    print("   ❌ WEAKNESSES:")
    print("      • Prone to overfitting")
    print("      • High variance with data changes")
    print("      • Biased toward features with more levels")
    print("      • Can create overly complex trees")

def business_recommendations(results_df):
    """Provide business recommendations"""
    print("\n" + "="*70)
    print("BUSINESS RECOMMENDATIONS")
    print("="*70)
    
    # Find best performing models
    best_overall = results_df.loc[results_df['CV_Mean'].idxmax()]
    fastest_training = results_df.loc[results_df['Training_Time'].idxmin()]
    most_balanced = results_df.loc[results_df['F1_Score'].idxmax()]
    
    print(f"\n🏆 BEST OVERALL PERFORMANCE:")
    print(f"   Model: {best_overall['Variant']}")
    print(f"   CV Accuracy: {best_overall['CV_Mean']:.4f}")
    print(f"   F1-Score: {best_overall['F1_Score']:.4f}")
    
    print(f"\n⚡ FASTEST TRAINING:")
    print(f"   Model: {fastest_training['Variant']}")
    print(f"   Training Time: {fastest_training['Training_Time']:.3f}s")
    print(f"   CV Accuracy: {fastest_training['CV_Mean']:.4f}")
    
    print(f"\n⚖️ MOST BALANCED:")
    print(f"   Model: {most_balanced['Variant']}")
    print(f"   F1-Score: {most_balanced['F1_Score']:.4f}")
    print(f"   Precision: {most_balanced['Precision']:.4f}")
    print(f"   Recall: {most_balanced['Recall']:.4f}")
    
    print(f"\n💼 DEPLOYMENT RECOMMENDATIONS:")
    print(f"   🚀 PRODUCTION SYSTEM: Use {best_overall['Algorithm']}")
    print(f"      • Highest cross-validation accuracy")
    print(f"      • Good generalization capability")
    print(f"      • Suitable for automated filtering")
    
    print(f"\n   ⏱️  REAL-TIME SYSTEM: Use {fastest_training['Algorithm']}")
    print(f"      • Fastest training and retraining")
    print(f"      • Quick adaptation to new scam patterns")
    print(f"      • Low computational overhead")
    
    print(f"\n   📊 EXPLAINABLE AI: Use Decision Trees")
    print(f"      • Clear decision paths")
    print(f"      • Regulatory compliance")
    print(f"      • Easy to audit and explain")

def main():
    """Main comparison function"""
    # Load data
    df = load_and_prepare_data()
    
    # Train all algorithms
    results_df = train_all_algorithms(df)
    
    # Analyze results
    analyze_algorithm_strengths(results_df)
    
    # Explain characteristics
    explain_algorithm_characteristics()
    
    # Business recommendations
    business_recommendations(results_df)
    
    # Show detailed results table
    print("\n" + "="*70)
    print("DETAILED RESULTS TABLE")
    print("="*70)
    
    # Sort by CV accuracy
    results_sorted = results_df.sort_values('CV_Mean', ascending=False)
    
    print("\nRanked by Cross-Validation Accuracy:")
    print("-" * 80)
    for i, (_, row) in enumerate(results_sorted.iterrows(), 1):
        print(f"{i}. {row['Variant']:<25} | CV: {row['CV_Mean']:.4f} | F1: {row['F1_Score']:.4f} | Time: {row['Training_Time']:.3f}s")
    
    print("\n" + "="*70)
    print("FINAL CONCLUSION")
    print("="*70)
    print("Based on this comprehensive analysis:")
    print("• SVM shows strong performance for scam detection")
    print("• Naive Bayes provides excellent speed-accuracy balance")
    print("• Decision Trees offer interpretability at cost of performance")
    print("• Choice depends on specific business requirements")
    print("• All algorithms can effectively detect Nigerian scam patterns")
    
    # Save results
    results_df.to_csv('comprehensive_algorithm_comparison.csv', index=False)
    print(f"\nDetailed results saved to: comprehensive_algorithm_comparison.csv")

if __name__ == "__main__":
    main()
