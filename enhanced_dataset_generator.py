#!/usr/bin/env python3
"""
Enhanced Dataset Generator for Nigerian Scam Detection
Generates additional realistic data patterns to supplement web scraping
"""

import pandas as pd
import random
from datetime import datetime
import json

class EnhancedDatasetGenerator:
    def __init__(self):
        self.data = []
        self.id_counter = 1
        
        # Nigerian banks and financial institutions
        self.nigerian_banks = [
            "First Bank", "GTBank", "Access Bank", "Zenith Bank", "UBA", "Fidelity Bank",
            "Sterling Bank", "Union Bank", "Wema Bank", "FCMB", "Stanbic IBTC", "Ecobank"
        ]
        
        # Nigerian government agencies
        self.gov_agencies = [
            "EFCC", "ICPC", "CBN", "FCCPC", "SEC", "NDIC", "PENCOM", "NAICOM",
            "CAC", "FIRS", "NCC", "NERC", "NIMASA", "FRSC", "SON", "NBS"
        ]
        
        # Common Nigerian locations
        self.locations = [
            "Lagos", "Abuja", "Kano", "Ibadan", "Port Harcourt", "Benin City",
            "Kaduna", "<PERSON>s", "Ilorin", "A<PERSON>", "Onits<PERSON>", "<PERSON><PERSON>", "Enugu", "Calabar"
        ]

    def generate_advance_fee_scams(self, count=20):
        """Generate advance fee fraud (419) scam messages"""
        templates = [
            "I am {name} from {location}. I have ${amount} to transfer. I need your help and will give you {percentage}% commission.",
            "Greetings! I am {title} {name}. Due to political situation in {location}, I need to transfer ${amount}. You will receive {percentage}% for your assistance.",
            "CONFIDENTIAL: I am {name}, a {profession} in {location}. I have access to ${amount} in dormant account. Contact me for details.",
            "Dear Friend, I am {name} from {bank}. We have ${amount} belonging to deceased client. You can claim as next of kin for {percentage}% share.",
            "URGENT: I am {name}, widow of late politician from {location}. I need to transfer ${amount} before government seizes it. You get {percentage}%.",
            "Hello, I am {title} {name} from {agency}. We recovered ${amount} from corrupt officials. You can help us transfer for {percentage}% commission.",
            "Greetings in the name of Allah. I am {name} from {location}. I have ${amount} inheritance to transfer. Islamic law requires foreign partner.",
            "Dear Sir/Madam, I am {name}, a diplomat from {location}. I have ${amount} in diplomatic bag. Need your account for safe keeping.",
            "PRIVATE: I am {name} from {bank} {location}. Customer died in plane crash leaving ${amount}. You can claim as relative for {percentage}%.",
            "Attention: I am {name}, oil contractor in {location}. Government owes me ${amount}. Need foreign account to receive payment."
        ]
        
        names = ["Johnson Adebayo", "Sarah Ibrahim", "Michael Okafor", "Fatima Hassan", "David Ogundimu", "Grace Okoro", "Ahmed Bello", "Mary Eze"]
        titles = ["Dr.", "Prof.", "Engr.", "Barr.", "Chief", "Alhaji", "Mrs.", "Mr."]
        professions = ["banker", "lawyer", "engineer", "contractor", "businessman", "civil servant", "diplomat", "auditor"]
        
        for _ in range(count):
            template = random.choice(templates)
            message = template.format(
                name=random.choice(names),
                title=random.choice(titles),
                location=random.choice(self.locations),
                bank=random.choice(self.nigerian_banks),
                agency=random.choice(self.gov_agencies),
                profession=random.choice(professions),
                amount=random.choice([2500000, 5000000, 8500000, ********, ********, ********]),
                percentage=random.choice([20, 25, 30, 35, 40])
            )
            self.add_to_dataset(message, "ADVANCE_FEE_SCAM")

    def generate_romance_scams(self, count=15):
        """Generate romance scam messages"""
        templates = [
            "Hello my love, I am {name} from {location}. I saw your profile and fell in love. I am a {profession} and want to marry you.",
            "Dearest, I am {name}, a {profession} working in {location}. I love you so much and want to visit you. Send money for my visa.",
            "My darling, I am {name}. I am stuck in {location} and need ${amount} to come to you. I will pay back when we meet.",
            "Sweetheart, I am {name}, a widow/widower from {location}. I have ${amount} inheritance and want to share with you as my spouse.",
            "My love, I am {name}, a soldier in {location}. I found gold worth ${amount} and need your help to ship it. We will be rich together.",
            "Honey, I am {name} from {location}. I am coming to marry you but customs is asking for ${amount} tax. Please help me.",
            "Beloved, I am {name}, a doctor in {location}. I love you and want to relocate to your country. Send ${amount} for my travel documents.",
            "My heart, I am {name}. I have ${amount} in my account but bank is asking for ${amount2} to transfer to your country for our wedding.",
            "Darling, I am {name} from {location}. I am pregnant with your baby and need ${amount} for hospital bills. Please help.",
            "My soul mate, I am {name}. I won ${amount} lottery but need ${amount2} tax to claim it. We will share the money when I visit you."
        ]
        
        names = ["Jennifer Smith", "Robert Johnson", "Maria Garcia", "James Wilson", "Lisa Anderson", "Michael Brown", "Susan Davis", "David Miller"]
        professions = ["doctor", "engineer", "soldier", "businessman", "nurse", "teacher", "contractor", "oil worker"]
        
        for _ in range(count):
            template = random.choice(templates)
            message = template.format(
                name=random.choice(names),
                location=random.choice(self.locations + ["Syria", "Afghanistan", "Iraq", "Libya"]),
                profession=random.choice(professions),
                amount=random.choice([50000, 100000, 250000, 500000, 1000000]),
                amount2=random.choice([2000, 5000, 10000, 15000, 25000])
            )
            self.add_to_dataset(message, "ROMANCE_SCAM")

    def generate_investment_scams(self, count=15):
        """Generate investment scam messages"""
        templates = [
            "INVESTMENT OPPORTUNITY: Invest ₦{amount} in our {business} and get ₦{returns} in {days} days. {guarantee}% guaranteed returns!",
            "FOREX TRADING: Learn our secret strategy and earn ${daily} daily. Pay ₦{fee} for training materials and software.",
            "CRYPTOCURRENCY: New {coin} coin launching. Invest ₦{amount} now and become millionaire in {months} months. Limited slots!",
            "REAL ESTATE: Buy land in {location} for ₦{amount}. Property will triple in value within {years} years. Guaranteed profit!",
            "AGRICULTURE: Invest in our {crop} farm. ₦{amount} investment returns ₦{returns} after {months} months harvest.",
            "BINARY OPTIONS: Trade with our robot and earn {percentage}% daily. Minimum investment ₦{amount}. No losses guaranteed!",
            "PONZI SCHEME: Join our network and earn ₦{amount} monthly. Refer {people} people and get bonus ₦{bonus}.",
            "MINING: Invest in our {mineral} mining operation. ₦{amount} investment yields ₦{returns} in {months} months.",
            "ONLINE BUSINESS: Start earning ₦{amount} daily from home. Pay ₦{fee} registration fee and begin immediately.",
            "STOCK TRADING: Our AI system guarantees {percentage}% monthly returns. Invest minimum ₦{amount} to start."
        ]
        
        businesses = ["oil business", "gold mining", "agriculture", "real estate", "import/export", "cryptocurrency"]
        crops = ["cassava", "yam", "rice", "maize", "cocoa", "palm oil"]
        minerals = ["gold", "diamond", "crude oil", "coal", "limestone"]
        coins = ["NairaCoin", "AfricaCoin", "LagosToken", "NigeriaToken", "AbujaCoin"]
        
        for _ in range(count):
            template = random.choice(templates)
            message = template.format(
                amount=random.choice([10000, 25000, 50000, 100000, 250000, 500000]),
                returns=random.choice([50000, 100000, 250000, 500000, 1000000, 2500000]),
                days=random.choice([7, 14, 21, 30, 45, 60]),
                months=random.choice([3, 6, 9, 12, 18, 24]),
                years=random.choice([2, 3, 5]),
                guarantee=random.choice([100, 200, 300, 500]),
                daily=random.choice([100, 200, 500, 1000]),
                fee=random.choice([5000, 10000, 15000, 25000, 50000]),
                percentage=random.choice([10, 15, 20, 25, 30, 50]),
                people=random.choice([3, 5, 10, 15, 20]),
                bonus=random.choice([10000, 25000, 50000, 100000]),
                business=random.choice(businesses),
                crop=random.choice(crops),
                mineral=random.choice(minerals),
                coin=random.choice(coins),
                location=random.choice(self.locations)
            )
            self.add_to_dataset(message, "INVESTMENT_SCAM")

    def generate_legitimate_messages(self, count=25):
        """Generate legitimate financial and government messages"""
        templates = [
            "{bank} announces new savings account with {rate}% annual interest rate. Visit any branch to open account.",
            "{agency} releases new guidelines for {service}. All stakeholders should comply with regulations by {date}.",
            "CBN sets new exchange rate: USD/NGN {rate}. All authorized dealers must comply immediately.",
            "{bank} introduces new mobile banking features including bill payment and fund transfer services.",
            "EFCC recovers ₦{amount} billion from corruption cases. Funds will be returned to government treasury.",
            "{agency} warns citizens against {scam_type}. Always verify through official channels before making payments.",
            "Nigeria's inflation rate stands at {rate}% according to latest NBS report. Government working on solutions.",
            "{bank} customers can now access loans up to ₦{amount} million with {rate}% interest rate.",
            "SEC approves new mutual fund with minimum investment of ₦{amount}. Fund targets {sector} sector.",
            "NDIC increases deposit insurance coverage to ₦{amount} per depositor in all commercial banks."
        ]
        
        services = ["business registration", "tax payment", "pension contribution", "insurance claims", "loan applications"]
        scam_types = ["fake lottery", "advance fee fraud", "romance scams", "investment fraud", "employment scams"]
        sectors = ["agriculture", "technology", "manufacturing", "oil and gas", "telecommunications"]
        
        for _ in range(count):
            template = random.choice(templates)
            message = template.format(
                bank=random.choice(self.nigerian_banks),
                agency=random.choice(self.gov_agencies),
                rate=random.choice([5.5, 7.2, 8.5, 12.3, 15.7, 18.5]),
                amount=random.choice([500, 1000, 2500, 5000, 10000]),
                service=random.choice(services),
                scam_type=random.choice(scam_types),
                sector=random.choice(sectors),
                date="December 31, 2024"
            )
            self.add_to_dataset(message, "LEGITIMATE_FINANCIAL")

    def add_to_dataset(self, text, label):
        """Add text to dataset"""
        self.data.append({
            'ID': self.id_counter,
            'Text Message / Email / Chat': text,
            'Label': label
        })
        self.id_counter += 1

    def generate_complete_dataset(self):
        """Generate complete dataset with all types"""
        print("Generating comprehensive Nigerian scam dataset...")
        
        self.generate_advance_fee_scams(30)
        self.generate_romance_scams(20)
        self.generate_investment_scams(25)
        self.generate_legitimate_messages(35)
        
        print(f"Generated {len(self.data)} additional records")
        return self.data

    def save_dataset(self, filename="enhanced_dataset.csv"):
        """Save dataset to file"""
        df = pd.DataFrame(self.data)
        df.to_csv(filename, index=False, encoding='utf-8')
        print(f"Enhanced dataset saved to {filename}")

def main():
    generator = EnhancedDatasetGenerator()
    generator.generate_complete_dataset()
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"enhanced_dataset_{timestamp}.csv"
    generator.save_dataset(filename)
    
    # Show summary
    label_counts = {}
    for record in generator.data:
        label = record['Label']
        label_counts[label] = label_counts.get(label, 0) + 1
    
    print(f"\nEnhanced Dataset Summary:")
    print(f"Total records: {len(generator.data)}")
    for label, count in sorted(label_counts.items()):
        print(f"  {label}: {count} records")

if __name__ == "__main__":
    main()
