Model,Test_Accuracy,CV_Mean,CV_Std,Training_Time,Prediction_Time,Model_Object
Linear SVM (Basic),0.75,0.7555555555555555,0.06285393610547094,0.08939075469970703,0.012069940567016602,"Pipeline(steps=[('tfidf',
                 TfidfVectorizer(max_features=1000, stop_words='english')),
                ('svm', LinearSVC(max_iter=2000, random_state=42))])"
Linear SVM (Optimized),0.75,0.8222222222222223,0.13698697784375505,0.09298467636108398,0.008017301559448242,"Pipeline(steps=[('tfidf',
                 TfidfVectorizer(max_features=2000, ngram_range=(1, 2),
                                 stop_words='english')),
                ('svm', LinearSVC(C=10.0, max_iter=2000, random_state=42))])"
RBF SVM (Gaussian Kernel),0.75,0.7555555555555555,0.06285393610547094,0.022948503494262695,0.0029952526092529297,"Pipeline(steps=[('tfidf',
                 TfidfVectorizer(max_features=1000, stop_words='english')),
                ('svm', SVC(probability=True, random_state=42))])"
Polynomial SVM,0.75,0.7555555555555555,0.06285393610547094,0.012000799179077148,0.010004520416259766,"Pipeline(steps=[('tfidf',
                 TfidfVectorizer(max_features=800, stop_words='english')),
                ('svm', SVC(kernel='poly', probability=True, random_state=42))])"
