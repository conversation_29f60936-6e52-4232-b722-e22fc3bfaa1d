#!/usr/bin/env python3
"""
Support Vector Machine (SVM) Analysis for Nigerian Scam Detection
Comprehensive explanation of how SVM works with the scam dataset
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.svm import SVC, LinearSVC
from sklearn.metrics import classification_report, accuracy_score, confusion_matrix
from sklearn.pipeline import Pipeline
import re
import time
import warnings
warnings.filterwarnings('ignore')

class SVMScamDetectionAnalysis:
    def __init__(self, dataset_path):
        """Initialize SVM analysis for scam detection"""
        self.dataset_path = dataset_path
        self.df = None
        self.X_train = None
        self.X_test = None
        self.y_train = None
        self.y_test = None
        self.svm_models = {}
        
    def load_and_explore_data(self):
        """Load and explore the scam dataset"""
        print("="*70)
        print("SVM SCAM DETECTION ANALYSIS")
        print("="*70)
        
        self.df = pd.read_csv(self.dataset_path)
        print(f"Dataset loaded: {self.df.shape[0]} rows, {self.df.shape[1]} columns")
        
        # Show label distribution
        print(f"\nLabel distribution:")
        label_counts = self.df['Label'].value_counts()
        for label, count in label_counts.items():
            print(f"  {label}: {count}")
        
        # Create binary classification
        scam_labels = ['SCAM_SAMPLE', 'SCAM_WARNING', 'SCAM_ALERT']
        self.df['is_scam'] = self.df['Label'].apply(lambda x: 1 if x in scam_labels else 0)
        
        print(f"\nBinary classification:")
        print(f"  Scam messages: {self.df['is_scam'].sum()}")
        print(f"  Legitimate messages: {(self.df['is_scam'] == 0).sum()}")
        print(f"  Scam percentage: {self.df['is_scam'].mean():.1%}")
        
        return self.df
    
    def preprocess_text_data(self):
        """Preprocess text data for SVM"""
        print("\n" + "="*70)
        print("TEXT PREPROCESSING FOR SVM")
        print("="*70)
        
        def clean_text(text):
            """Clean text for SVM processing"""
            if pd.isna(text):
                return ""
            
            # Convert to lowercase
            text = text.lower()
            
            # Remove special characters but keep spaces
            text = re.sub(r'[^\w\s]', ' ', text)
            
            # Remove extra whitespace
            text = re.sub(r'\s+', ' ', text).strip()
            
            return text
        
        # Apply text cleaning
        self.df['cleaned_text'] = self.df['Text Message / Email / Chat'].apply(clean_text)
        
        # Remove very short messages
        self.df = self.df[self.df['cleaned_text'].str.len() >= 10].reset_index(drop=True)
        
        print(f"After preprocessing: {len(self.df)} messages")
        
        # Show sample cleaned texts
        print(f"\nSample cleaned texts:")
        for i, (text, label) in enumerate(zip(self.df['cleaned_text'].head(3), self.df['Label'].head(3))):
            print(f"{i+1}. [{label}] {text[:80]}...")
        
        return self.df
    
    def explain_svm_theory(self):
        """Explain SVM theory in context of scam detection"""
        print("\n" + "="*70)
        print("HOW SVM WORKS FOR SCAM DETECTION")
        print("="*70)
        
        print("🎯 SUPPORT VECTOR MACHINE FUNDAMENTALS:")
        print("   • SVM finds the optimal hyperplane that separates scam from legitimate messages")
        print("   • The hyperplane maximizes the margin between the two classes")
        print("   • Support vectors are the data points closest to the decision boundary")
        print("   • These support vectors define the optimal separating hyperplane")
        
        print("\n📊 IN TEXT CLASSIFICATION CONTEXT:")
        print("   • Each word becomes a dimension in high-dimensional space")
        print("   • TF-IDF values represent coordinates in this space")
        print("   • SVM finds the best way to separate scam and legitimate text points")
        print("   • The decision boundary is a hyperplane in this word-space")
        
        print("\n🔍 EXAMPLE WITH OUR SCAM DATA:")
        print("   • Dimension 1: TF-IDF score for word 'money'")
        print("   • Dimension 2: TF-IDF score for word 'urgent'")
        print("   • Dimension 3: TF-IDF score for word 'bank'")
        print("   • ... (thousands of dimensions for all words)")
        print("   • SVM finds hyperplane: w₁×'money' + w₂×'urgent' + w₃×'bank' + ... = threshold")
        
        print("\n⚖️ MARGIN MAXIMIZATION:")
        print("   • SVM doesn't just find any separating line")
        print("   • It finds the line with maximum distance to nearest points")
        print("   • This provides better generalization to new, unseen messages")
        print("   • Robust to outliers and noise in the data")
    
    def train_different_svm_variants(self):
        """Train different SVM variants and compare"""
        print("\n" + "="*70)
        print("TRAINING DIFFERENT SVM VARIANTS")
        print("="*70)
        
        # Prepare data
        X = self.df['cleaned_text']
        y = self.df['is_scam']
        
        self.X_train, self.X_test, self.y_train, self.y_test = train_test_split(
            X, y, test_size=0.3, random_state=42, stratify=y
        )
        
        print(f"Training set: {len(self.X_train)} samples")
        print(f"Test set: {len(self.X_test)} samples")
        
        # Define different SVM configurations
        svm_configs = {
            'Linear SVM (Basic)': Pipeline([
                ('tfidf', TfidfVectorizer(max_features=1000, stop_words='english')),
                ('svm', LinearSVC(C=1.0, random_state=42, max_iter=2000))
            ]),
            
            'Linear SVM (Optimized)': Pipeline([
                ('tfidf', TfidfVectorizer(max_features=2000, stop_words='english', ngram_range=(1, 2))),
                ('svm', LinearSVC(C=10.0, random_state=42, max_iter=2000))
            ]),
            
            'RBF SVM (Gaussian Kernel)': Pipeline([
                ('tfidf', TfidfVectorizer(max_features=1000, stop_words='english')),
                ('svm', SVC(kernel='rbf', C=1.0, gamma='scale', random_state=42, probability=True))
            ]),
            
            'Polynomial SVM': Pipeline([
                ('tfidf', TfidfVectorizer(max_features=800, stop_words='english')),
                ('svm', SVC(kernel='poly', degree=3, C=1.0, random_state=42, probability=True))
            ])
        }
        
        # Train and evaluate each SVM variant
        results = []
        
        for name, model in svm_configs.items():
            print(f"\n🔧 Training {name}...")
            
            # Time the training
            start_time = time.time()
            model.fit(self.X_train, self.y_train)
            training_time = time.time() - start_time
            
            # Make predictions
            start_time = time.time()
            y_pred = model.predict(self.X_test)
            prediction_time = time.time() - start_time
            
            # Calculate metrics
            accuracy = accuracy_score(self.y_test, y_pred)
            
            # Cross-validation
            cv_scores = cross_val_score(model, self.X_train, self.y_train, cv=3, scoring='accuracy')
            
            # Store results
            result = {
                'Model': name,
                'Test_Accuracy': accuracy,
                'CV_Mean': cv_scores.mean(),
                'CV_Std': cv_scores.std(),
                'Training_Time': training_time,
                'Prediction_Time': prediction_time,
                'Model_Object': model
            }
            
            results.append(result)
            self.svm_models[name] = model
            
            print(f"   ✅ Accuracy: {accuracy:.4f}")
            print(f"   📊 CV Score: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")
            print(f"   ⏱️  Training: {training_time:.3f}s, Prediction: {prediction_time:.3f}s")
        
        return pd.DataFrame(results)
    
    def analyze_svm_decision_boundary(self):
        """Analyze how SVM makes decisions"""
        print("\n" + "="*70)
        print("SVM DECISION BOUNDARY ANALYSIS")
        print("="*70)
        
        # Get the best performing linear SVM
        linear_svm = self.svm_models.get('Linear SVM (Optimized)')
        if not linear_svm:
            print("No linear SVM model found!")
            return
        
        # Get the trained components
        vectorizer = linear_svm.named_steps['tfidf']
        classifier = linear_svm.named_steps['svm']
        
        # Get feature names and weights
        feature_names = vectorizer.get_feature_names_out()
        weights = classifier.coef_[0]  # Linear SVM weights
        
        print(f"📊 SVM DECISION FUNCTION:")
        print(f"   • Total features (words): {len(feature_names)}")
        print(f"   • Decision function: f(x) = Σ(wᵢ × xᵢ) + b")
        print(f"   • Bias term (b): {classifier.intercept_[0]:.4f}")
        print(f"   • Classification: SCAM if f(x) > 0, LEGITIMATE if f(x) < 0")
        
        # Find most important features for scam detection
        feature_importance = list(zip(feature_names, weights))
        feature_importance.sort(key=lambda x: abs(x[1]), reverse=True)
        
        print(f"\n🚨 TOP SCAM INDICATORS (Positive weights):")
        scam_features = [(name, weight) for name, weight in feature_importance if weight > 0][:10]
        for i, (feature, weight) in enumerate(scam_features, 1):
            print(f"   {i:2}. {feature:15} (weight: +{weight:.4f})")
        
        print(f"\n✅ TOP LEGITIMATE INDICATORS (Negative weights):")
        legit_features = [(name, weight) for name, weight in feature_importance if weight < 0][:10]
        for i, (feature, weight) in enumerate(legit_features, 1):
            print(f"   {i:2}. {feature:15} (weight: {weight:.4f})")
        
        print(f"\n🔍 INTERPRETATION:")
        print(f"   • Positive weights push decision toward SCAM")
        print(f"   • Negative weights push decision toward LEGITIMATE")
        print(f"   • Larger absolute weights = stronger influence")
        print(f"   • Final decision = sum of all weighted features + bias")
    
    def demonstrate_svm_predictions(self):
        """Demonstrate SVM predictions on sample messages"""
        print("\n" + "="*70)
        print("SVM PREDICTION DEMONSTRATIONS")
        print("="*70)
        
        # Sample test messages
        test_messages = [
            {
                'text': "Congratulations! You won $1,000,000 in lottery. Send bank details immediately.",
                'expected': 'SCAM',
                'type': 'Lottery Scam'
            },
            {
                'text': "URGENT: Your account will be closed. Verify details now or lose access.",
                'expected': 'SCAM', 
                'type': 'Phishing Scam'
            },
            {
                'text': "Investment opportunity: Double money in 30 days guaranteed returns.",
                'expected': 'SCAM',
                'type': 'Investment Scam'
            },
            {
                'text': "Central Bank of Nigeria announces new exchange rate effective immediately.",
                'expected': 'LEGITIMATE',
                'type': 'Official Announcement'
            },
            {
                'text': "EFCC warns citizens against advance fee fraud through official channels.",
                'expected': 'LEGITIMATE',
                'type': 'Official Warning'
            }
        ]
        
        # Get the best SVM model
        best_svm = self.svm_models.get('Linear SVM (Optimized)')
        if not best_svm:
            print("No SVM model available for demonstration!")
            return
        
        print("🧪 TESTING SVM ON SAMPLE MESSAGES:")
        print("-" * 70)
        
        for i, msg_data in enumerate(test_messages, 1):
            message = msg_data['text']
            expected = msg_data['expected']
            msg_type = msg_data['type']
            
            # Make prediction
            prediction = best_svm.predict([message])[0]
            result = "SCAM" if prediction == 1 else "LEGITIMATE"
            
            # Get decision function score (distance from hyperplane)
            decision_score = best_svm.decision_function([message])[0]
            confidence = abs(decision_score)
            
            print(f"\n📧 TEST {i}: {msg_type}")
            print(f"Message: {message}")
            print(f"Expected: {expected}")
            print(f"SVM Prediction: {result}")
            print(f"Decision Score: {decision_score:.4f}")
            print(f"Confidence: {confidence:.4f}")
            
            # Explain the decision
            if decision_score > 0:
                print(f"💡 Explanation: Score > 0 → Classified as SCAM")
            else:
                print(f"💡 Explanation: Score < 0 → Classified as LEGITIMATE")
            
            # Check if correct
            if result == expected:
                print("✅ CORRECT prediction!")
            else:
                print("❌ INCORRECT prediction!")
    
    def explain_svm_advantages_disadvantages(self):
        """Explain SVM advantages and disadvantages for scam detection"""
        print("\n" + "="*70)
        print("SVM ADVANTAGES & DISADVANTAGES FOR SCAM DETECTION")
        print("="*70)
        
        print("✅ SVM ADVANTAGES:")
        print("   1. EFFECTIVE IN HIGH DIMENSIONS:")
        print("      • Excels with thousands of text features (words)")
        print("      • No curse of dimensionality issues")
        print("      • Maintains performance even when features > samples")
        
        print("\n   2. MEMORY EFFICIENT:")
        print("      • Only stores support vectors (subset of training data)")
        print("      • Compact model representation")
        print("      • Fast prediction once trained")
        
        print("\n   3. VERSATILE KERNELS:")
        print("      • Linear kernel: Fast, interpretable")
        print("      • RBF kernel: Captures non-linear patterns")
        print("      • Polynomial kernel: Models feature interactions")
        
        print("\n   4. ROBUST TO OUTLIERS:")
        print("      • Focus on support vectors near decision boundary")
        print("      • Less influenced by extreme data points")
        print("      • Good generalization capability")
        
        print("\n   5. STRONG THEORETICAL FOUNDATION:")
        print("      • Based on statistical learning theory")
        print("      • Structural risk minimization principle")
        print("      • Guaranteed global optimum")
        
        print("\n❌ SVM DISADVANTAGES:")
        print("   1. NO PROBABILITY ESTIMATES:")
        print("      • Only provides binary classification")
        print("      • No confidence scores (unless modified)")
        print("      • Harder to set decision thresholds")
        
        print("\n   2. SENSITIVE TO FEATURE SCALING:")
        print("      • Requires normalized features")
        print("      • TF-IDF helps but preprocessing crucial")
        print("      • Different scales can bias results")
        
        print("\n   3. HYPERPARAMETER TUNING:")
        print("      • C parameter controls regularization")
        print("      • Kernel parameters need optimization")
        print("      • Grid search can be computationally expensive")
        
        print("\n   4. TRAINING TIME:")
        print("      • O(n²) to O(n³) complexity")
        print("      • Slower than Naive Bayes on large datasets")
        print("      • Non-linear kernels especially slow")
        
        print("\n   5. INTERPRETABILITY:")
        print("      • Linear SVM: Interpretable weights")
        print("      • Non-linear kernels: Black box decisions")
        print("      • Harder to explain to business stakeholders")

def main():
    """Main function to run SVM analysis"""
    
    # Initialize analysis
    svm_analysis = SVMScamDetectionAnalysis('scam_dataset_20250704_180433.csv')
    
    # Run complete analysis
    svm_analysis.load_and_explore_data()
    svm_analysis.preprocess_text_data()
    svm_analysis.explain_svm_theory()
    results_df = svm_analysis.train_different_svm_variants()
    svm_analysis.analyze_svm_decision_boundary()
    svm_analysis.demonstrate_svm_predictions()
    svm_analysis.explain_svm_advantages_disadvantages()
    
    # Show final results summary
    print("\n" + "="*70)
    print("SVM ANALYSIS SUMMARY")
    print("="*70)
    
    print("\n📊 MODEL PERFORMANCE COMPARISON:")
    results_sorted = results_df.sort_values('CV_Mean', ascending=False)
    for _, row in results_sorted.iterrows():
        print(f"   {row['Model']:<25}: {row['CV_Mean']:.4f} CV accuracy")
    
    best_model = results_sorted.iloc[0]
    print(f"\n🏆 BEST SVM MODEL: {best_model['Model']}")
    print(f"   Cross-validation accuracy: {best_model['CV_Mean']:.4f}")
    print(f"   Training time: {best_model['Training_Time']:.3f} seconds")
    
    print("\n💡 KEY INSIGHTS:")
    print("   • Linear SVM often performs best for text classification")
    print("   • Feature engineering (TF-IDF, n-grams) crucial for performance")
    print("   • SVM provides excellent generalization with proper regularization")
    print("   • Decision boundary analysis reveals important scam indicators")
    print("   • Trade-off between accuracy and interpretability")
    
    # Save results
    results_df.to_csv('svm_analysis_results.csv', index=False)
    print(f"\n💾 Results saved to: svm_analysis_results.csv")

if __name__ == "__main__":
    main()
