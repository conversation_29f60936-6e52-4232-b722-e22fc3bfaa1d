#!/usr/bin/env python3
"""
Simple Random Forest Analysis for Nigerian Scam Detection
Focus on core Random Forest concepts and practical implementation
"""

import pandas as pd
import numpy as np
import json
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import classification_report, accuracy_score, precision_score, recall_score, f1_score
from sklearn.pipeline import Pipeline
import re
import time

def load_and_prepare_data():
    """Load and prepare the JSON dataset"""
    print("="*60)
    print("RANDOM FOREST SCAM DETECTION ANALYSIS")
    print("="*60)
    
    # Load JSON data
    with open('nigerian_scam_dataset_20250704_183721.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    df = pd.DataFrame(data)
    print(f"Dataset: {df.shape[0]} rows, {df.shape[1]} columns")
    
    # Show label distribution
    print(f"\nLabel distribution:")
    for label, count in df['Label'].value_counts().items():
        print(f"  {label}: {count}")
    
    # Create binary classification
    scam_labels = ['ADVANCE_FEE_SCAM', 'LOTTERY_SCAM', 'ROMANCE_SCAM', 'INVESTMENT_SCAM', 
                  'PHISHING_SCAM', 'EMPLOYMENT_SCAM', 'CHARITY_SCAM']
    df['is_scam'] = df['Label'].apply(lambda x: 1 if x in scam_labels else 0)
    
    print(f"\nBinary classification:")
    print(f"  Scam: {df['is_scam'].sum()}, Legitimate: {(df['is_scam'] == 0).sum()}")
    print(f"  Scam percentage: {df['is_scam'].mean():.1%}")
    
    # Clean text
    def clean_text(text):
        if pd.isna(text):
            return ""
        text = text.lower()
        text = re.sub(r'[^\w\s]', ' ', text)
        text = re.sub(r'\s+', ' ', text).strip()
        return text
    
    df['cleaned_text'] = df['Text Message / Email / Chat'].apply(clean_text)
    df = df[df['cleaned_text'].str.len() >= 10].reset_index(drop=True)
    
    print(f"After preprocessing: {len(df)} messages")
    return df

def explain_random_forest_fundamentals():
    """Explain Random Forest fundamentals for scam detection"""
    print("\n" + "="*60)
    print("HOW RANDOM FOREST WORKS FOR SCAM DETECTION")
    print("="*60)
    
    print("🌳 CORE CONCEPT:")
    print("   Random Forest = Ensemble of many decision trees")
    print("   Each tree votes, majority wins the classification")
    
    print("\n📊 IN TEXT CLASSIFICATION:")
    print("   • Each tree learns from different word combinations")
    print("   • Bootstrap sampling: each tree sees different messages")
    print("   • Random features: each split uses subset of words")
    print("   • Final prediction: average of all tree votes")
    
    print("\n🔍 EXAMPLE:")
    print("   Tree 1: if 'money' > 0.5 and 'urgent' > 0.3 → SCAM")
    print("   Tree 2: if 'bank' > 0.4 and 'account' > 0.2 → SCAM")
    print("   Tree 3: if 'lottery' > 0.6 → SCAM")
    print("   Final: 3 trees vote SCAM → Result: SCAM")
    
    print("\n⚖️ WHY ENSEMBLE WORKS:")
    print("   • Reduces overfitting (individual trees may overfit)")
    print("   • Improves generalization (combines multiple perspectives)")
    print("   • Provides confidence scores (% of trees voting)")
    print("   • Handles noise better than single tree")

def train_random_forest_models(df):
    """Train different Random Forest models"""
    print("\n" + "="*60)
    print("TRAINING RANDOM FOREST MODELS")
    print("="*60)
    
    # Prepare data
    X = df['cleaned_text']
    y = df['is_scam']
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)
    
    print(f"Training: {len(X_train)}, Test: {len(X_test)}")
    
    # Define Random Forest models
    models = {
        'RF Basic': Pipeline([
            ('tfidf', TfidfVectorizer(max_features=1000, stop_words='english')),
            ('rf', RandomForestClassifier(n_estimators=50, random_state=42))
        ]),
        'RF Optimized': Pipeline([
            ('tfidf', TfidfVectorizer(max_features=2000, stop_words='english', ngram_range=(1, 2))),
            ('rf', RandomForestClassifier(n_estimators=100, max_depth=15, min_samples_split=5, random_state=42))
        ]),
        'RF Balanced': Pipeline([
            ('tfidf', TfidfVectorizer(max_features=1500, stop_words='english')),
            ('rf', RandomForestClassifier(n_estimators=100, class_weight='balanced', random_state=42))
        ])
    }
    
    results = []
    trained_models = {}
    
    for name, model in models.items():
        print(f"\n🌳 Training {name}...")
        
        # Train model
        start_time = time.time()
        model.fit(X_train, y_train)
        training_time = time.time() - start_time
        
        # Test model
        y_pred = model.predict(X_test)
        accuracy = accuracy_score(y_test, y_pred)
        precision = precision_score(y_test, y_pred, zero_division=0)
        recall = recall_score(y_test, y_pred, zero_division=0)
        f1 = f1_score(y_test, y_pred, zero_division=0)
        
        # Cross-validation
        cv_scores = cross_val_score(model, X_train, y_train, cv=5)
        
        results.append({
            'Model': name,
            'Accuracy': accuracy,
            'Precision': precision,
            'Recall': recall,
            'F1_Score': f1,
            'CV_Mean': cv_scores.mean(),
            'CV_Std': cv_scores.std(),
            'Training_Time': training_time
        })
        
        trained_models[name] = model
        
        print(f"   Accuracy: {accuracy:.4f}")
        print(f"   Precision: {precision:.4f}, Recall: {recall:.4f}, F1: {f1:.4f}")
        print(f"   CV Score: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")
        print(f"   Training Time: {training_time:.3f}s")
    
    return pd.DataFrame(results), trained_models, X_test, y_test

def analyze_feature_importance(model):
    """Analyze Random Forest feature importance"""
    print("\n" + "="*60)
    print("RANDOM FOREST FEATURE IMPORTANCE")
    print("="*60)
    
    # Get components
    vectorizer = model.named_steps['tfidf']
    classifier = model.named_steps['rf']
    
    # Get feature importance
    feature_names = vectorizer.get_feature_names_out()
    importance_scores = classifier.feature_importances_
    
    print(f"📊 FEATURE IMPORTANCE ANALYSIS:")
    print(f"   Total features: {len(feature_names)}")
    print(f"   Number of trees: {classifier.n_estimators}")
    print(f"   Importance = average decrease in impurity across trees")
    
    # Sort features by importance
    feature_importance = list(zip(feature_names, importance_scores))
    feature_importance.sort(key=lambda x: x[1], reverse=True)
    
    print(f"\n🚨 TOP 15 MOST IMPORTANT FEATURES:")
    for i, (feature, importance) in enumerate(feature_importance[:15], 1):
        print(f"   {i:2}. {feature:20} (importance: {importance:.6f})")
    
    # Calculate feature concentration
    sorted_importance = sorted(importance_scores, reverse=True)
    cumulative_importance = np.cumsum(sorted_importance)
    total_importance = cumulative_importance[-1]
    features_80_percent = np.where(cumulative_importance >= 0.8 * total_importance)[0][0] + 1
    
    print(f"\n📈 FEATURE CONCENTRATION:")
    print(f"   Top {features_80_percent} features account for 80% of importance")
    print(f"   This shows RF focuses on most discriminative words")

def test_random_forest_predictions(model):
    """Test Random Forest on sample messages"""
    print("\n" + "="*60)
    print("RANDOM FOREST PREDICTION EXAMPLES")
    print("="*60)
    
    test_messages = [
        ("I am banker from Nigeria with $50 million inheritance. Need your help.", "SCAM"),
        ("Congratulations! You won $2,000,000 lottery. Contact immediately.", "SCAM"),
        ("URGENT: Account suspended. Click link to verify banking details.", "SCAM"),
        ("Central Bank announces new monetary policy next quarter.", "LEGITIMATE"),
        ("EFCC warns against advance fee fraud schemes.", "LEGITIMATE")
    ]
    
    for i, (message, expected) in enumerate(test_messages, 1):
        prediction = model.predict([message])[0]
        result = "SCAM" if prediction == 1 else "LEGITIMATE"
        
        # Get probability scores
        probabilities = model.predict_proba([message])[0]
        prob_legitimate = probabilities[0]
        prob_scam = probabilities[1]
        
        print(f"\n📧 Test {i}:")
        print(f"   Message: {message}")
        print(f"   Expected: {expected}")
        print(f"   RF Prediction: {result}")
        print(f"   Probabilities: Legitimate {prob_legitimate:.3f}, Scam {prob_scam:.3f}")
        print(f"   Status: {'✅ CORRECT' if result == expected else '❌ WRONG'}")

def explain_random_forest_advantages():
    """Explain Random Forest advantages and disadvantages"""
    print("\n" + "="*60)
    print("RANDOM FOREST ADVANTAGES & DISADVANTAGES")
    print("="*60)
    
    print("✅ ADVANTAGES:")
    print("   1. ROBUST TO OVERFITTING")
    print("      • Ensemble reduces variance")
    print("      • Bootstrap sampling adds diversity")
    
    print("\n   2. FEATURE IMPORTANCE")
    print("      • Automatically ranks word importance")
    print("      • Identifies key scam indicators")
    
    print("\n   3. PROBABILITY ESTIMATES")
    print("      • Provides confidence scores")
    print("      • Enables threshold tuning")
    
    print("\n   4. HANDLES COMPLEX PATTERNS")
    print("      • Captures feature interactions")
    print("      • Non-linear decision boundaries")
    
    print("\n   5. MISSING DATA TOLERANCE")
    print("      • Works with incomplete features")
    print("      • Robust to data quality issues")
    
    print("\n❌ DISADVANTAGES:")
    print("   1. MEMORY INTENSIVE")
    print("      • Stores multiple trees")
    print("      • Large memory footprint")
    
    print("\n   2. LESS INTERPRETABLE")
    print("      • Complex ensemble decisions")
    print("      • Harder to explain than single tree")
    
    print("\n   3. SLOWER PREDICTION")
    print("      • Must query all trees")
    print("      • May not suit real-time apps")
    
    print("\n   4. HYPERPARAMETER TUNING")
    print("      • Many parameters to optimize")
    print("      • Grid search time-consuming")

def main():
    """Main analysis function"""
    # Load data
    df = load_and_prepare_data()
    
    # Explain Random Forest
    explain_random_forest_fundamentals()
    
    # Train models
    results_df, models, X_test, y_test = train_random_forest_models(df)
    
    # Analyze best model
    best_model_name = results_df.loc[results_df['CV_Mean'].idxmax(), 'Model']
    best_model = models[best_model_name]
    
    print(f"\n🏆 BEST MODEL: {best_model_name}")
    
    # Analyze feature importance
    analyze_feature_importance(best_model)
    
    # Test predictions
    test_random_forest_predictions(best_model)
    
    # Explain advantages
    explain_random_forest_advantages()
    
    print("\n" + "="*60)
    print("RANDOM FOREST SUMMARY")
    print("="*60)
    
    # Show results table
    print("\nModel Performance Comparison:")
    results_sorted = results_df.sort_values('CV_Mean', ascending=False)
    for _, row in results_sorted.iterrows():
        print(f"  {row['Model']:<15}: CV {row['CV_Mean']:.4f}, F1 {row['F1_Score']:.4f}")
    
    best_row = results_sorted.iloc[0]
    print(f"\n🏆 Best Random Forest: {best_row['Model']}")
    print(f"   CV Accuracy: {best_row['CV_Mean']:.4f}")
    print(f"   F1-Score: {best_row['F1_Score']:.4f}")
    print(f"   Training Time: {best_row['Training_Time']:.3f}s")
    
    print("\n💡 KEY INSIGHTS:")
    print("• Random Forest excels at scam detection through ensemble learning")
    print("• Feature importance reveals most discriminative words automatically")
    print("• Probability scores enable confidence-based decision making")
    print("• Robust to overfitting through bootstrap aggregation")
    print("• Excellent balance of accuracy and interpretability")
    
    # Save results
    results_df.to_csv('random_forest_results.csv', index=False)
    print(f"\nResults saved to: random_forest_results.csv")

if __name__ == "__main__":
    main()
