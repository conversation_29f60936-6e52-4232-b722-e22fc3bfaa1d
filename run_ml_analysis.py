#!/usr/bin/env python3
"""
Simple script to run machine learning analysis on the scam dataset
This script handles dependency installation and runs the analysis
"""

import subprocess
import sys
import os

def install_dependencies():
    """Install required dependencies"""
    print("Installing required dependencies...")
    
    dependencies = [
        'pandas>=1.5.0',
        'scikit-learn>=1.3.0',
        'matplotlib>=3.6.0',
        'seaborn>=0.12.0',
        'numpy>=1.24.0'
    ]
    
    for dep in dependencies:
        try:
            print(f"Installing {dep}...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', dep])
        except subprocess.CalledProcessError as e:
            print(f"Warning: Failed to install {dep}: {e}")
            continue
    
    print("Dependencies installation completed!")

def run_basic_analysis():
    """Run basic analysis without complex dependencies"""
    print("\n" + "="*60)
    print("BASIC SCAM DATASET ANALYSIS")
    print("="*60)
    
    try:
        import pandas as pd
        import numpy as np
        from collections import Counter
        import re
        
        # Find dataset file
        import glob
        dataset_files = glob.glob("nigerian_scam_dataset_*.csv")
        if not dataset_files:
            dataset_files = glob.glob("scam_dataset_*.csv")
        
        if not dataset_files:
            print("No dataset file found!")
            return
        
        latest_dataset = max(dataset_files, key=os.path.getctime)
        print(f"Using dataset: {latest_dataset}")
        
        # Load data
        df = pd.read_csv(latest_dataset)
        print(f"\nDataset loaded: {df.shape[0]} rows, {df.shape[1]} columns")
        
        # Basic statistics
        print(f"\nColumns: {list(df.columns)}")
        print(f"\nLabel distribution:")
        label_counts = df['Label'].value_counts()
        print(label_counts)
        
        # Create binary classification
        scam_labels = ['ADVANCE_FEE_SCAM', 'ROMANCE_SCAM', 'INVESTMENT_SCAM', 'SCAM_SAMPLE', 'SCAM_ALERT', 'SCAM_WARNING']
        df['is_scam'] = df['Label'].apply(lambda x: 1 if x in scam_labels else 0)
        
        print(f"\nBinary classification:")
        print(f"Scam messages: {df['is_scam'].sum()}")
        print(f"Legitimate messages: {(df['is_scam'] == 0).sum()}")
        print(f"Scam percentage: {df['is_scam'].mean():.1%}")
        
        # Text analysis
        df['text_length'] = df['Text Message / Email / Chat'].str.len()
        df['word_count'] = df['Text Message / Email / Chat'].str.split().str.len()
        
        print(f"\nText statistics:")
        print(f"Average text length: {df['text_length'].mean():.1f} characters")
        print(f"Average word count: {df['word_count'].mean():.1f} words")
        
        print(f"\nText length by category:")
        stats = df.groupby('is_scam')[['text_length', 'word_count']].agg(['mean', 'median'])
        print(stats)
        
        # Common words analysis
        def get_common_words(texts, n=10):
            all_text = ' '.join(texts).lower()
            words = re.findall(r'\b[a-z]+\b', all_text)
            # Filter out common stop words
            stop_words = {'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'a', 'an', 'this', 'that', 'these', 'those'}
            words = [w for w in words if w not in stop_words and len(w) > 2]
            return Counter(words).most_common(n)
        
        scam_texts = df[df['is_scam'] == 1]['Text Message / Email / Chat'].tolist()
        legit_texts = df[df['is_scam'] == 0]['Text Message / Email / Chat'].tolist()
        
        print(f"\nTop words in SCAM messages:")
        scam_words = get_common_words(scam_texts, 10)
        for word, count in scam_words:
            print(f"  {word}: {count}")
        
        print(f"\nTop words in LEGITIMATE messages:")
        legit_words = get_common_words(legit_texts, 10)
        for word, count in legit_words:
            print(f"  {word}: {count}")
        
        # Sample messages
        print(f"\nSample SCAM messages:")
        scam_samples = df[df['is_scam'] == 1]['Text Message / Email / Chat'].head(3)
        for i, msg in enumerate(scam_samples, 1):
            print(f"  {i}. {msg[:100]}...")
        
        print(f"\nSample LEGITIMATE messages:")
        legit_samples = df[df['is_scam'] == 0]['Text Message / Email / Chat'].head(3)
        for i, msg in enumerate(legit_samples, 1):
            print(f"  {i}. {msg[:100]}...")
        
        return df
        
    except ImportError as e:
        print(f"Missing dependency: {e}")
        print("Please install pandas: pip install pandas")
        return None
    except Exception as e:
        print(f"Error during analysis: {e}")
        return None

def run_ml_analysis():
    """Run machine learning analysis if scikit-learn is available"""
    try:
        from sklearn.model_selection import train_test_split
        from sklearn.feature_extraction.text import TfidfVectorizer
        from sklearn.naive_bayes import MultinomialNB
        from sklearn.linear_model import LogisticRegression
        from sklearn.metrics import classification_report, accuracy_score
        from sklearn.pipeline import Pipeline
        import pandas as pd
        
        print("\n" + "="*60)
        print("MACHINE LEARNING ANALYSIS")
        print("="*60)
        
        # Load data
        import glob
        dataset_files = glob.glob("nigerian_scam_dataset_*.csv")
        if not dataset_files:
            dataset_files = glob.glob("scam_dataset_*.csv")
        
        if not dataset_files:
            print("No dataset file found!")
            return
        
        latest_dataset = max(dataset_files, key=os.path.getctime)
        df = pd.read_csv(latest_dataset)
        
        # Prepare data
        scam_labels = ['ADVANCE_FEE_SCAM', 'ROMANCE_SCAM', 'INVESTMENT_SCAM', 'SCAM_SAMPLE', 'SCAM_ALERT', 'SCAM_WARNING']
        df['is_scam'] = df['Label'].apply(lambda x: 1 if x in scam_labels else 0)
        
        # Clean text
        df['cleaned_text'] = df['Text Message / Email / Chat'].str.lower().str.replace(r'[^\w\s]', '', regex=True)
        
        # Split data
        X = df['cleaned_text']
        y = df['is_scam']
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)
        
        print(f"Training samples: {len(X_train)}")
        print(f"Test samples: {len(X_test)}")
        
        # Train models
        models = {
            'Naive Bayes': Pipeline([
                ('tfidf', TfidfVectorizer(max_features=1000, stop_words='english')),
                ('nb', MultinomialNB())
            ]),
            'Logistic Regression': Pipeline([
                ('tfidf', TfidfVectorizer(max_features=1000, stop_words='english')),
                ('lr', LogisticRegression(random_state=42, max_iter=1000))
            ])
        }
        
        results = {}
        for name, model in models.items():
            print(f"\nTraining {name}...")
            model.fit(X_train, y_train)
            y_pred = model.predict(X_test)
            accuracy = accuracy_score(y_test, y_pred)
            results[name] = accuracy
            print(f"{name} Accuracy: {accuracy:.4f}")
        
        # Best model
        best_model_name = max(results.keys(), key=lambda x: results[x])
        best_model = models[best_model_name]
        
        print(f"\nBest model: {best_model_name} ({results[best_model_name]:.4f})")
        
        # Test predictions
        test_messages = [
            "Congratulations! You have won $1,000,000 in the lottery. Send your bank details.",
            "Central Bank of Nigeria announces new exchange rate of 461.50 NGN per USD.",
            "URGENT: Your account will be suspended. Click here to verify immediately.",
            "EFCC warns citizens against advance fee fraud. Always verify before payments."
        ]
        
        print(f"\nSample predictions:")
        for i, msg in enumerate(test_messages, 1):
            pred = best_model.predict([msg])[0]
            result = "SCAM" if pred == 1 else "LEGITIMATE"
            print(f"  {i}. {result}: {msg[:60]}...")
        
    except ImportError as e:
        print(f"Scikit-learn not available: {e}")
        print("Run: pip install scikit-learn")
    except Exception as e:
        print(f"Error in ML analysis: {e}")

def main():
    """Main function"""
    print("Nigerian Scam Detection - Machine Learning Analysis")
    print("="*60)
    
    # Check if we should install dependencies
    try:
        import pandas
        import sklearn
        print("Dependencies already installed!")
    except ImportError:
        install_dependencies()
    
    # Run basic analysis
    df = run_basic_analysis()
    
    if df is not None:
        # Run ML analysis if possible
        run_ml_analysis()
    
    print("\n" + "="*60)
    print("ANALYSIS COMPLETE!")
    print("="*60)
    print("Your dataset is ready for machine learning!")
    print("For advanced analysis, run: python scam_detection_model.py")

if __name__ == "__main__":
    main()
