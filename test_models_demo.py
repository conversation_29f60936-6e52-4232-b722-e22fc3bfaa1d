#!/usr/bin/env python3
"""
Demo script to test the trained models on sample messages
Shows why Na<PERSON> Bayes excels in scam detection
"""

import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.naive_bayes import MultinomialNB
from sklearn.svm import LinearSVC
from sklearn.tree import DecisionTreeClassifier
from sklearn.pipeline import Pipeline
import re

def load_and_train_models():
    """Load dataset and train the three main models"""
    print("Loading dataset and training models...")
    
    # Load data
    df = pd.read_csv('enhanced_dataset_20250705_181331.csv')
    
    # Prepare data
    scam_labels = ['ADVANCE_FEE_SCAM', 'ROMANCE_SCAM', 'INVESTMENT_SCAM', 'SCAM_SAMPLE']
    df['is_scam'] = df['Label'].apply(lambda x: 1 if x in scam_labels else 0)
    
    def clean_text(text):
        if pd.isna(text):
            return ""
        text = text.lower()
        text = re.sub(r'[^\w\s]', ' ', text)
        text = re.sub(r'\s+', ' ', text).strip()
        return text
    
    df['cleaned_text'] = df['Text Message / Email / Chat'].apply(clean_text)
    
    # Split data
    X = df['cleaned_text']
    y = df['is_scam']
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)
    
    # Define and train models
    models = {
        'Naive Bayes': Pipeline([
            ('tfidf', TfidfVectorizer(max_features=3000, stop_words='english')),
            ('nb', MultinomialNB())
        ]),
        'Linear SVM': Pipeline([
            ('tfidf', TfidfVectorizer(max_features=3000, stop_words='english')),
            ('svm', LinearSVC(random_state=42, max_iter=2000))
        ]),
        'Decision Tree': Pipeline([
            ('tfidf', TfidfVectorizer(max_features=2000, stop_words='english')),
            ('dt', DecisionTreeClassifier(random_state=42, max_depth=10))
        ])
    }
    
    # Train all models
    trained_models = {}
    for name, model in models.items():
        model.fit(X_train, y_train)
        trained_models[name] = model
        print(f"✓ {name} trained")
    
    return trained_models

def test_sample_messages(models):
    """Test models on sample messages"""
    print("\n" + "="*70)
    print("TESTING MODELS ON SAMPLE MESSAGES")
    print("="*70)
    
    # Test messages
    test_messages = [
        {
            'text': "Congratulations! You have won $1,000,000 in the Nigerian lottery. Send your bank details to claim your prize immediately.",
            'expected': 'SCAM',
            'type': 'Classic Lottery Scam'
        },
        {
            'text': "URGENT: Your account will be suspended unless you verify your details. Click this link and enter your password now.",
            'expected': 'SCAM',
            'type': 'Phishing Scam'
        },
        {
            'text': "Investment opportunity: Double your money in 30 days with our guaranteed forex trading system. Minimum investment ₦50,000.",
            'expected': 'SCAM',
            'type': 'Investment Scam'
        },
        {
            'text': "Central Bank of Nigeria announces new exchange rate of 461.50 NGN per USD effective immediately.",
            'expected': 'LEGITIMATE',
            'type': 'Official Financial News'
        },
        {
            'text': "EFCC warns citizens against advance fee fraud. Always verify through official channels before making payments.",
            'expected': 'LEGITIMATE',
            'type': 'Official Warning'
        },
        {
            'text': "Nigerian Stock Exchange recorded 2.3% gain in market capitalization this week according to latest reports.",
            'expected': 'LEGITIMATE',
            'type': 'Market Update'
        }
    ]
    
    # Test each message
    for i, msg_data in enumerate(test_messages, 1):
        message = msg_data['text']
        expected = msg_data['expected']
        msg_type = msg_data['type']
        
        print(f"\n📧 TEST MESSAGE {i}: {msg_type}")
        print(f"Text: {message[:80]}...")
        print(f"Expected: {expected}")
        print("-" * 50)
        
        # Test with each model
        for model_name, model in models.items():
            prediction = model.predict([message])[0]
            result = "SCAM" if prediction == 1 else "LEGITIMATE"
            
            # Get probability if available (Naive Bayes)
            if hasattr(model, 'predict_proba'):
                try:
                    proba = model.predict_proba([message])[0]
                    confidence = max(proba)
                    print(f"{model_name:12}: {result:10} (Confidence: {confidence:.1%})")
                except:
                    print(f"{model_name:12}: {result:10}")
            else:
                print(f"{model_name:12}: {result:10}")
        
        # Check if all models agree
        predictions = [model.predict([message])[0] for model in models.values()]
        if len(set(predictions)) == 1:
            print("✅ All models agree!")
        else:
            print("⚠️  Models disagree")

def analyze_naive_bayes_probabilities(nb_model):
    """Analyze Naive Bayes probability distributions"""
    print("\n" + "="*70)
    print("NAIVE BAYES PROBABILITY ANALYSIS")
    print("="*70)
    
    # Get the trained components
    vectorizer = nb_model.named_steps['tfidf']
    classifier = nb_model.named_steps['nb']
    
    # Get feature names
    feature_names = vectorizer.get_feature_names_out()
    
    # Get log probabilities for each class
    log_prob_scam = classifier.feature_log_prob_[1]  # Scam class
    log_prob_legit = classifier.feature_log_prob_[0]  # Legitimate class
    
    # Calculate log probability ratios
    log_prob_ratio = log_prob_scam - log_prob_legit
    
    print("\n🚨 TOP SCAM INDICATORS (words that strongly suggest scam):")
    top_scam_indices = log_prob_ratio.argsort()[-15:][::-1]
    for i, idx in enumerate(top_scam_indices, 1):
        word = feature_names[idx]
        ratio = log_prob_ratio[idx]
        print(f"{i:2}. {word:15} (log ratio: {ratio:+.3f})")
    
    print("\n✅ TOP LEGITIMATE INDICATORS (words that suggest legitimate):")
    top_legit_indices = log_prob_ratio.argsort()[:15]
    for i, idx in enumerate(top_legit_indices, 1):
        word = feature_names[idx]
        ratio = log_prob_ratio[idx]
        print(f"{i:2}. {word:15} (log ratio: {ratio:+.3f})")
    
    print("\n💡 INTERPRETATION:")
    print("• Positive ratios indicate words more likely in scam messages")
    print("• Negative ratios indicate words more likely in legitimate messages")
    print("• Higher absolute values = stronger indicators")

def explain_naive_bayes_advantages():
    """Explain why Naive Bayes works so well"""
    print("\n" + "="*70)
    print("WHY NAIVE BAYES EXCELS IN SCAM DETECTION")
    print("="*70)
    
    print("\n🎯 1. PERFECT FIT FOR TEXT CLASSIFICATION:")
    print("   • Bag-of-words model aligns with independence assumption")
    print("   • Each word contributes independently to scam probability")
    print("   • Example: P(scam | 'money', 'urgent') = P('money'|scam) × P('urgent'|scam)")
    
    print("\n⚡ 2. COMPUTATIONAL EFFICIENCY:")
    print("   • Training: Simply count word frequencies in each class")
    print("   • Prediction: Sum log probabilities (fast matrix operations)")
    print("   • Memory: Only stores word probability distributions")
    
    print("\n📊 3. HANDLES SPARSE DATA EXCELLENTLY:")
    print("   • Text data is naturally sparse (most words are absent)")
    print("   • Smoothing handles unseen word combinations gracefully")
    print("   • No curse of dimensionality issues")
    
    print("\n🛡️ 4. ROBUST WITH LIMITED DATA:")
    print("   • Requires fewer training examples than complex models")
    print("   • Less prone to overfitting")
    print("   • Probabilistic nature provides good generalization")
    
    print("\n🎲 5. PROBABILISTIC OUTPUT:")
    print("   • Provides confidence scores for predictions")
    print("   • Enables threshold-based filtering")
    print("   • Supports risk-based decision making")
    
    print("\n🔧 6. MINIMAL HYPERPARAMETER TUNING:")
    print("   • Works well with default parameters")
    print("   • Only alpha (smoothing) parameter needs occasional adjustment")
    print("   • Robust to feature selection choices")

def main():
    """Main demo function"""
    print("🎯 ML Model Comparison Demo")
    print("Testing SVM vs Decision Trees vs Naive Bayes")
    print("="*70)
    
    # Train models
    models = load_and_train_models()
    
    # Test sample messages
    test_sample_messages(models)
    
    # Analyze Naive Bayes probabilities
    analyze_naive_bayes_probabilities(models['Naive Bayes'])
    
    # Explain advantages
    explain_naive_bayes_advantages()
    
    print("\n" + "="*70)
    print("🏆 CONCLUSION")
    print("="*70)
    print("Naive Bayes consistently excels in scam detection because:")
    print("1. Text classification naturally fits the independence assumption")
    print("2. Efficient handling of high-dimensional sparse text features")
    print("3. Robust performance with limited training data")
    print("4. Fast training and prediction times")
    print("5. Probabilistic output useful for business decisions")
    print("6. Minimal tuning required for good performance")

if __name__ == "__main__":
    main()
