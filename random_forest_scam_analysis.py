#!/usr/bin/env python3
"""
Random Forest Analysis for Nigerian Scam Detection
Comprehensive analysis using the JSON dataset
"""

import pandas as pd
import numpy as np
import json
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import classification_report, accuracy_score, confusion_matrix
from sklearn.metrics import precision_score, recall_score, f1_score
from sklearn.pipeline import Pipeline
import re
import time
import warnings
warnings.filterwarnings('ignore')

class RandomForestScamAnalysis:
    def __init__(self, json_file):
        """Initialize Random Forest analysis"""
        self.json_file = json_file
        self.df = None
        self.rf_models = {}
        
    def load_and_explore_data(self):
        """Load and explore the JSON dataset"""
        print("="*70)
        print("RANDOM FOREST SCAM DETECTION ANALYSIS")
        print("="*70)
        
        # Load JSON data
        with open(self.json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        self.df = pd.DataFrame(data)
        print(f"Dataset loaded: {self.df.shape[0]} rows, {self.df.shape[1]} columns")
        
        # Show label distribution
        print(f"\nLabel distribution:")
        label_counts = self.df['Label'].value_counts()
        for label, count in label_counts.items():
            print(f"  {label}: {count}")
        
        # Create binary classification
        scam_labels = ['ADVANCE_FEE_SCAM', 'LOTTERY_SCAM', 'ROMANCE_SCAM', 'INVESTMENT_SCAM', 
                      'PHISHING_SCAM', 'EMPLOYMENT_SCAM', 'CHARITY_SCAM']
        self.df['is_scam'] = self.df['Label'].apply(lambda x: 1 if x in scam_labels else 0)
        
        print(f"\nBinary classification:")
        print(f"  Scam messages: {self.df['is_scam'].sum()}")
        print(f"  Legitimate messages: {(self.df['is_scam'] == 0).sum()}")
        print(f"  Scam percentage: {self.df['is_scam'].mean():.1%}")
        
        return self.df
    
    def preprocess_text_data(self):
        """Preprocess text data for Random Forest"""
        print("\n" + "="*70)
        print("TEXT PREPROCESSING FOR RANDOM FOREST")
        print("="*70)
        
        def clean_text(text):
            """Clean text for processing"""
            if pd.isna(text):
                return ""
            
            # Convert to lowercase
            text = text.lower()
            
            # Remove special characters but keep spaces
            text = re.sub(r'[^\w\s]', ' ', text)
            
            # Remove extra whitespace
            text = re.sub(r'\s+', ' ', text).strip()
            
            return text
        
        # Apply text cleaning
        self.df['cleaned_text'] = self.df['Text Message / Email / Chat'].apply(clean_text)
        
        # Remove very short messages
        self.df = self.df[self.df['cleaned_text'].str.len() >= 10].reset_index(drop=True)
        
        print(f"After preprocessing: {len(self.df)} messages")
        
        # Show sample cleaned texts
        print(f"\nSample cleaned texts:")
        for i, (text, label) in enumerate(zip(self.df['cleaned_text'].head(3), self.df['Label'].head(3))):
            print(f"{i+1}. [{label}] {text[:80]}...")
        
        return self.df
    
    def explain_random_forest_theory(self):
        """Explain Random Forest theory for scam detection"""
        print("\n" + "="*70)
        print("HOW RANDOM FOREST WORKS FOR SCAM DETECTION")
        print("="*70)
        
        print("🌳 RANDOM FOREST FUNDAMENTALS:")
        print("   • Ensemble of multiple decision trees")
        print("   • Each tree trained on random subset of data (bootstrap sampling)")
        print("   • Each tree uses random subset of features at each split")
        print("   • Final prediction = majority vote of all trees")
        
        print("\n📊 IN TEXT CLASSIFICATION CONTEXT:")
        print("   • Each word (TF-IDF feature) can be used for tree splits")
        print("   • Trees learn different patterns from different data subsets")
        print("   • Random feature selection reduces overfitting")
        print("   • Ensemble combines multiple weak learners into strong classifier")
        
        print("\n🔍 EXAMPLE WITH SCAM DATA:")
        print("   Tree 1: if 'money' > 0.5 and 'urgent' > 0.3 → SCAM")
        print("   Tree 2: if 'bank' > 0.4 and 'account' > 0.2 → SCAM")
        print("   Tree 3: if 'lottery' > 0.6 → SCAM")
        print("   Final: Majority vote from all trees determines classification")
        
        print("\n⚖️ BOOTSTRAP AGGREGATING (BAGGING):")
        print("   • Each tree sees different training samples")
        print("   • Reduces variance and overfitting")
        print("   • Improves generalization to new messages")
        print("   • Provides natural uncertainty estimation")
        
        print("\n🎲 RANDOM FEATURE SELECTION:")
        print("   • At each split, only subset of features considered")
        print("   • Typically √(total_features) for classification")
        print("   • Reduces correlation between trees")
        print("   • Makes ensemble more robust")
    
    def train_random_forest_variants(self):
        """Train different Random Forest configurations"""
        print("\n" + "="*70)
        print("TRAINING RANDOM FOREST VARIANTS")
        print("="*70)
        
        # Prepare data
        X = self.df['cleaned_text']
        y = self.df['is_scam']
        
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        print(f"Training set: {len(X_train)} samples")
        print(f"Test set: {len(X_test)} samples")
        
        # Define different Random Forest configurations
        rf_configs = {
            'RF Basic': Pipeline([
                ('tfidf', TfidfVectorizer(max_features=1000, stop_words='english')),
                ('rf', RandomForestClassifier(n_estimators=50, random_state=42))
            ]),
            
            'RF Optimized': Pipeline([
                ('tfidf', TfidfVectorizer(max_features=2000, stop_words='english', ngram_range=(1, 2))),
                ('rf', RandomForestClassifier(n_estimators=100, max_depth=15, min_samples_split=5, random_state=42))
            ]),
            
            'RF Deep': Pipeline([
                ('tfidf', TfidfVectorizer(max_features=1500, stop_words='english')),
                ('rf', RandomForestClassifier(n_estimators=200, max_depth=20, min_samples_split=2, random_state=42))
            ]),
            
            'RF Balanced': Pipeline([
                ('tfidf', TfidfVectorizer(max_features=1200, stop_words='english', ngram_range=(1, 2))),
                ('rf', RandomForestClassifier(n_estimators=100, max_depth=10, min_samples_split=10, 
                                            class_weight='balanced', random_state=42))
            ])
        }
        
        # Train and evaluate each Random Forest variant
        results = []
        
        for name, model in rf_configs.items():
            print(f"\n🌳 Training {name}...")
            
            # Time the training
            start_time = time.time()
            model.fit(X_train, y_train)
            training_time = time.time() - start_time
            
            # Make predictions
            start_time = time.time()
            y_pred = model.predict(X_test)
            prediction_time = time.time() - start_time
            
            # Calculate metrics
            accuracy = accuracy_score(y_test, y_pred)
            precision = precision_score(y_test, y_pred, zero_division=0)
            recall = recall_score(y_test, y_pred, zero_division=0)
            f1 = f1_score(y_test, y_pred, zero_division=0)
            
            # Cross-validation
            cv_scores = cross_val_score(model, X_train, y_train, cv=5, scoring='accuracy')
            
            # Store results
            result = {
                'Model': name,
                'Test_Accuracy': accuracy,
                'Precision': precision,
                'Recall': recall,
                'F1_Score': f1,
                'CV_Mean': cv_scores.mean(),
                'CV_Std': cv_scores.std(),
                'Training_Time': training_time,
                'Prediction_Time': prediction_time,
                'Model_Object': model
            }
            
            results.append(result)
            self.rf_models[name] = model
            
            print(f"   ✅ Accuracy: {accuracy:.4f}")
            print(f"   📊 Precision: {precision:.4f}, Recall: {recall:.4f}, F1: {f1:.4f}")
            print(f"   🔄 CV Score: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")
            print(f"   ⏱️  Training: {training_time:.3f}s, Prediction: {prediction_time:.3f}s")
        
        return pd.DataFrame(results), X_test, y_test
    
    def analyze_feature_importance(self):
        """Analyze Random Forest feature importance"""
        print("\n" + "="*70)
        print("RANDOM FOREST FEATURE IMPORTANCE ANALYSIS")
        print("="*70)
        
        # Get the best performing Random Forest
        best_rf = self.rf_models.get('RF Optimized')
        if not best_rf:
            print("No Random Forest model found!")
            return
        
        # Get the trained components
        vectorizer = best_rf.named_steps['tfidf']
        classifier = best_rf.named_steps['rf']
        
        # Get feature names and importance scores
        feature_names = vectorizer.get_feature_names_out()
        importance_scores = classifier.feature_importances_
        
        print(f"📊 RANDOM FOREST FEATURE IMPORTANCE:")
        print(f"   • Total features (words): {len(feature_names)}")
        print(f"   • Number of trees: {classifier.n_estimators}")
        print(f"   • Feature importance = average decrease in impurity across all trees")
        print(f"   • Higher scores = more important for classification")
        
        # Find most important features
        feature_importance = list(zip(feature_names, importance_scores))
        feature_importance.sort(key=lambda x: x[1], reverse=True)
        
        print(f"\n🚨 TOP 15 MOST IMPORTANT FEATURES:")
        for i, (feature, importance) in enumerate(feature_importance[:15], 1):
            print(f"   {i:2}. {feature:20} (importance: {importance:.6f})")
        
        print(f"\n🔍 INTERPRETATION:")
        print(f"   • Features with high importance are frequently used for splits")
        print(f"   • These words are most discriminative between scam and legitimate")
        print(f"   • Random Forest automatically identifies key scam indicators")
        print(f"   • Ensemble approach reduces bias toward single features")
        
        # Calculate cumulative importance
        sorted_importance = sorted(importance_scores, reverse=True)
        cumulative_importance = np.cumsum(sorted_importance)
        total_importance = cumulative_importance[-1]
        
        # Find how many features account for 80% of importance
        features_80_percent = np.where(cumulative_importance >= 0.8 * total_importance)[0][0] + 1
        print(f"\n📈 FEATURE CONCENTRATION:")
        print(f"   • Top {features_80_percent} features account for 80% of total importance")
        print(f"   • This shows Random Forest focuses on most discriminative words")
        
        return feature_importance[:20]  # Return top 20 for further analysis
    
    def demonstrate_rf_predictions(self):
        """Demonstrate Random Forest predictions with probability scores"""
        print("\n" + "="*70)
        print("RANDOM FOREST PREDICTION DEMONSTRATIONS")
        print("="*70)
        
        # Sample test messages
        test_messages = [
            {
                'text': "Dear friend, I am banker from Nigeria with $50 million inheritance. Need your help to transfer.",
                'expected': 'SCAM',
                'type': 'Advance Fee Scam'
            },
            {
                'text': "Congratulations! You won $2,000,000 in international lottery. Contact us immediately.",
                'expected': 'SCAM', 
                'type': 'Lottery Scam'
            },
            {
                'text': "URGENT: Your account will be suspended. Click link to verify your banking details now.",
                'expected': 'SCAM',
                'type': 'Phishing Scam'
            },
            {
                'text': "Central Bank of Nigeria announces new monetary policy effective next quarter.",
                'expected': 'LEGITIMATE',
                'type': 'Official Announcement'
            },
            {
                'text': "EFCC warns citizens against advance fee fraud and urges reporting suspicious activities.",
                'expected': 'LEGITIMATE',
                'type': 'Official Warning'
            }
        ]
        
        # Get the best Random Forest model
        best_rf = self.rf_models.get('RF Optimized')
        if not best_rf:
            print("No Random Forest model available for demonstration!")
            return
        
        print("🧪 TESTING RANDOM FOREST ON SAMPLE MESSAGES:")
        print("-" * 70)
        
        for i, msg_data in enumerate(test_messages, 1):
            message = msg_data['text']
            expected = msg_data['expected']
            msg_type = msg_data['type']
            
            # Make prediction
            prediction = best_rf.predict([message])[0]
            result = "SCAM" if prediction == 1 else "LEGITIMATE"
            
            # Get probability scores
            probabilities = best_rf.predict_proba([message])[0]
            prob_legitimate = probabilities[0]
            prob_scam = probabilities[1]
            confidence = max(prob_legitimate, prob_scam)
            
            print(f"\n📧 TEST {i}: {msg_type}")
            print(f"Message: {message}")
            print(f"Expected: {expected}")
            print(f"RF Prediction: {result}")
            print(f"Probability Scores:")
            print(f"  • Legitimate: {prob_legitimate:.4f} ({prob_legitimate*100:.1f}%)")
            print(f"  • Scam: {prob_scam:.4f} ({prob_scam*100:.1f}%)")
            print(f"Confidence: {confidence:.4f}")
            
            # Check if correct
            if result == expected:
                print("✅ CORRECT prediction!")
            else:
                print("❌ INCORRECT prediction!")
    
    def explain_rf_advantages_disadvantages(self):
        """Explain Random Forest advantages and disadvantages"""
        print("\n" + "="*70)
        print("RANDOM FOREST ADVANTAGES & DISADVANTAGES")
        print("="*70)
        
        print("✅ RANDOM FOREST ADVANTAGES:")
        print("   1. ROBUST TO OVERFITTING:")
        print("      • Ensemble of trees reduces variance")
        print("      • Bootstrap sampling prevents memorization")
        print("      • Random feature selection adds diversity")
        
        print("\n   2. HANDLES MISSING DATA:")
        print("      • Can work with incomplete features")
        print("      • Maintains accuracy with missing values")
        print("      • No need for extensive preprocessing")
        
        print("\n   3. FEATURE IMPORTANCE:")
        print("      • Automatically ranks feature importance")
        print("      • Identifies key scam indicators")
        print("      • Helps with feature selection")
        
        print("\n   4. PROBABILITY ESTIMATES:")
        print("      • Provides confidence scores")
        print("      • Enables threshold tuning")
        print("      • Better risk assessment")
        
        print("\n   5. HANDLES NON-LINEAR RELATIONSHIPS:")
        print("      • Captures complex feature interactions")
        print("      • No assumptions about data distribution")
        print("      • Flexible decision boundaries")
        
        print("\n   6. PARALLEL TRAINING:")
        print("      • Trees can be trained independently")
        print("      • Scales well with multiple cores")
        print("      • Faster training on large datasets")
        
        print("\n❌ RANDOM FOREST DISADVANTAGES:")
        print("   1. MEMORY INTENSIVE:")
        print("      • Stores multiple decision trees")
        print("      • Large memory footprint")
        print("      • Can be problematic for deployment")
        
        print("\n   2. LESS INTERPRETABLE:")
        print("      • Complex ensemble decisions")
        print("      • Harder to explain than single tree")
        print("      • Black box for individual predictions")
        
        print("\n   3. HYPERPARAMETER TUNING:")
        print("      • Many parameters to optimize")
        print("      • n_estimators, max_depth, min_samples_split")
        print("      • Grid search can be time-consuming")
        
        print("\n   4. PREDICTION TIME:")
        print("      • Must query all trees for prediction")
        print("      • Slower than single models")
        print("      • May not suit real-time applications")
        
        print("\n   5. BIAS TOWARD CATEGORICAL FEATURES:")
        print("      • Favors features with more categories")
        print("      • Can be problematic with mixed data types")
        print("      • May need feature engineering")

def main():
    """Main function to run Random Forest analysis"""
    
    # Initialize analysis
    rf_analysis = RandomForestScamAnalysis('nigerian_scam_dataset_20250704_183721.json')
    
    # Run complete analysis
    rf_analysis.load_and_explore_data()
    rf_analysis.preprocess_text_data()
    rf_analysis.explain_random_forest_theory()
    results_df, X_test, y_test = rf_analysis.train_random_forest_variants()
    top_features = rf_analysis.analyze_feature_importance()
    rf_analysis.demonstrate_rf_predictions()
    rf_analysis.explain_rf_advantages_disadvantages()
    
    # Show final results summary
    print("\n" + "="*70)
    print("RANDOM FOREST ANALYSIS SUMMARY")
    print("="*70)
    
    print("\n📊 MODEL PERFORMANCE COMPARISON:")
    results_sorted = results_df.sort_values('CV_Mean', ascending=False)
    for _, row in results_sorted.iterrows():
        print(f"   {row['Model']:<15}: {row['CV_Mean']:.4f} CV accuracy, F1: {row['F1_Score']:.4f}")
    
    best_model = results_sorted.iloc[0]
    print(f"\n🏆 BEST RANDOM FOREST MODEL: {best_model['Model']}")
    print(f"   Cross-validation accuracy: {best_model['CV_Mean']:.4f}")
    print(f"   F1-Score: {best_model['F1_Score']:.4f}")
    print(f"   Training time: {best_model['Training_Time']:.3f} seconds")
    
    print("\n💡 KEY INSIGHTS:")
    print("   • Random Forest provides excellent ensemble performance")
    print("   • Feature importance reveals key scam indicators automatically")
    print("   • Probability scores enable confidence-based filtering")
    print("   • Robust to overfitting through bootstrap aggregation")
    print("   • Handles complex feature interactions naturally")
    
    # Save results
    results_df.to_csv('random_forest_analysis_results.csv', index=False)
    print(f"\n💾 Results saved to: random_forest_analysis_results.csv")

if __name__ == "__main__":
    main()
