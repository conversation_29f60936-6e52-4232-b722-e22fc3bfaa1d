#!/usr/bin/env python3
"""
Simple SVM Analysis with Essential Graphs
Nigerian Scam Detection Dataset
"""

import pandas as pd
import numpy as np
import json
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.svm import LinearSVC
from sklearn.metrics import classification_report, accuracy_score, confusion_matrix
from sklearn.metrics import precision_score, recall_score, f1_score
from sklearn.pipeline import Pipeline
import re
import seaborn as sns

# Set style
plt.style.use('default')
sns.set_palette("Set2")

def load_data():
    """Load and prepare data"""
    print("="*50)
    print("SVM ANALYSIS WITH GRAPHS")
    print("="*50)
    
    # Load JSON data
    with open('nigerian_scam_dataset_20250704_183721.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    df = pd.DataFrame(data)
    print(f"Dataset: {df.shape[0]} rows")
    
    # Create binary classification
    scam_labels = ['ADVANCE_FEE_SCAM', 'LOTTERY_SCAM', 'ROMANCE_SCAM', 'INVESTMENT_SCAM', 
                  'PHISHING_SCAM', 'EMPLOYMENT_SCAM', 'CHARITY_SCAM', 'SCAM_SAMPLE', 
                  'SCAM_ALERT', 'SCAM_WARNING']
    df['is_scam'] = df['Label'].apply(lambda x: 1 if x in scam_labels else 0)
    
    print(f"Scam: {df['is_scam'].sum()}, Legitimate: {(df['is_scam'] == 0).sum()}")
    
    # Clean text
    def clean_text(text):
        if pd.isna(text):
            return ""
        text = text.lower()
        text = re.sub(r'[^\w\s]', ' ', text)
        text = re.sub(r'\s+', ' ', text).strip()
        return text
    
    df['cleaned_text'] = df['Text Message / Email / Chat'].apply(clean_text)
    df = df[df['cleaned_text'].str.len() >= 10].reset_index(drop=True)
    
    return df

def create_dataset_graphs(df):
    """Create basic dataset visualizations"""
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    fig.suptitle('Dataset Analysis', fontsize=14, fontweight='bold')
    
    # 1. Scam vs Legitimate distribution
    counts = df['is_scam'].value_counts()
    labels = ['Legitimate', 'Scam']
    colors = ['lightgreen', 'lightcoral']
    
    axes[0, 0].pie(counts.values, labels=labels, autopct='%1.1f%%', colors=colors, startangle=90)
    axes[0, 0].set_title('Scam vs Legitimate Messages')
    
    # 2. Message types
    top_labels = df['Label'].value_counts().head(8)
    axes[0, 1].barh(range(len(top_labels)), top_labels.values, color='skyblue')
    axes[0, 1].set_yticks(range(len(top_labels)))
    axes[0, 1].set_yticklabels(top_labels.index, fontsize=8)
    axes[0, 1].set_title('Top Message Types')
    axes[0, 1].set_xlabel('Count')
    
    # 3. Message length distribution
    df['msg_length'] = df['cleaned_text'].str.len()
    axes[1, 0].hist(df[df['is_scam'] == 0]['msg_length'], alpha=0.7, label='Legitimate', bins=15, color='lightgreen')
    axes[1, 0].hist(df[df['is_scam'] == 1]['msg_length'], alpha=0.7, label='Scam', bins=15, color='lightcoral')
    axes[1, 0].set_title('Message Length Distribution')
    axes[1, 0].set_xlabel('Characters')
    axes[1, 0].set_ylabel('Count')
    axes[1, 0].legend()
    
    # 4. Scam types breakdown
    scam_data = df[df['is_scam'] == 1]
    scam_counts = scam_data['Label'].value_counts().head(6)
    axes[1, 1].bar(range(len(scam_counts)), scam_counts.values, color='lightcoral')
    axes[1, 1].set_xticks(range(len(scam_counts)))
    axes[1, 1].set_xticklabels([label[:15] + '...' if len(label) > 15 else label for label in scam_counts.index], 
                              rotation=45, ha='right', fontsize=8)
    axes[1, 1].set_title('Scam Types')
    axes[1, 1].set_ylabel('Count')
    
    plt.tight_layout()
    plt.savefig('dataset_overview.png', dpi=150, bbox_inches='tight')
    plt.show()
    print("📊 Graph 1: dataset_overview.png created")

def train_svm_and_analyze(df):
    """Train SVM models and create performance graphs"""
    print("\n" + "="*50)
    print("TRAINING SVM MODELS")
    print("="*50)
    
    # Prepare data
    X = df['cleaned_text']
    y = df['is_scam']
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)
    
    # Define SVM models
    models = {
        'SVM (C=0.1)': Pipeline([
            ('tfidf', TfidfVectorizer(max_features=1000, stop_words='english')),
            ('svm', LinearSVC(C=0.1, random_state=42, max_iter=2000))
        ]),
        'SVM (C=1.0)': Pipeline([
            ('tfidf', TfidfVectorizer(max_features=1500, stop_words='english')),
            ('svm', LinearSVC(C=1.0, random_state=42, max_iter=2000))
        ]),
        'SVM (C=10.0)': Pipeline([
            ('tfidf', TfidfVectorizer(max_features=2000, stop_words='english')),
            ('svm', LinearSVC(C=10.0, random_state=42, max_iter=2000))
        ]),
        'SVM Optimized': Pipeline([
            ('tfidf', TfidfVectorizer(max_features=2000, stop_words='english', ngram_range=(1, 2))),
            ('svm', LinearSVC(C=10.0, random_state=42, max_iter=2000))
        ])
    }
    
    results = []
    best_model = None
    best_score = 0
    
    for name, model in models.items():
        print(f"Training {name}...")
        
        # Train and evaluate
        model.fit(X_train, y_train)
        y_pred = model.predict(X_test)
        
        accuracy = accuracy_score(y_test, y_pred)
        precision = precision_score(y_test, y_pred, zero_division=0)
        recall = recall_score(y_test, y_pred, zero_division=0)
        f1 = f1_score(y_test, y_pred, zero_division=0)
        
        # Cross-validation
        cv_scores = cross_val_score(model, X_train, y_train, cv=5)
        
        results.append({
            'Model': name,
            'Accuracy': accuracy,
            'Precision': precision,
            'Recall': recall,
            'F1_Score': f1,
            'CV_Mean': cv_scores.mean(),
            'CV_Std': cv_scores.std()
        })
        
        if cv_scores.mean() > best_score:
            best_score = cv_scores.mean()
            best_model = (model, y_test, y_pred, name)
        
        print(f"  CV Score: {cv_scores.mean():.4f}")
    
    return pd.DataFrame(results), best_model

def create_performance_graphs(results_df, best_model_data):
    """Create SVM performance graphs"""
    model, y_test, y_pred, model_name = best_model_data
    
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    fig.suptitle('SVM Performance Analysis', fontsize=14, fontweight='bold')
    
    # 1. Model comparison
    models = results_df['Model']
    cv_scores = results_df['CV_Mean']
    cv_stds = results_df['CV_Std']
    
    bars = axes[0, 0].bar(range(len(models)), cv_scores, yerr=cv_stds, capsize=5, color='skyblue')
    axes[0, 0].set_title('Cross-Validation Accuracy')
    axes[0, 0].set_ylabel('Accuracy')
    axes[0, 0].set_xticks(range(len(models)))
    axes[0, 0].set_xticklabels([m.replace('SVM ', '') for m in models], rotation=45)
    
    # Add values on bars
    for bar, score in zip(bars, cv_scores):
        axes[0, 0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                       f'{score:.3f}', ha='center', fontweight='bold')
    
    # 2. Metrics comparison
    metrics = ['Accuracy', 'Precision', 'Recall', 'F1_Score']
    x = np.arange(len(models))
    width = 0.2
    
    colors = ['lightblue', 'lightgreen', 'lightcoral', 'gold']
    for i, (metric, color) in enumerate(zip(metrics, colors)):
        axes[0, 1].bar(x + i*width, results_df[metric], width, label=metric, color=color, alpha=0.8)
    
    axes[0, 1].set_title('Performance Metrics')
    axes[0, 1].set_ylabel('Score')
    axes[0, 1].set_xticks(x + width * 1.5)
    axes[0, 1].set_xticklabels([m.replace('SVM ', '') for m in models], rotation=45)
    axes[0, 1].legend()
    axes[0, 1].set_ylim(0, 1.1)
    
    # 3. Confusion Matrix
    cm = confusion_matrix(y_test, y_pred)
    im = axes[1, 0].imshow(cm, interpolation='nearest', cmap='Blues')
    axes[1, 0].set_title(f'Confusion Matrix - {model_name}')
    
    # Add text annotations
    thresh = cm.max() / 2.
    for i in range(cm.shape[0]):
        for j in range(cm.shape[1]):
            axes[1, 0].text(j, i, format(cm[i, j], 'd'),
                           ha="center", va="center",
                           color="white" if cm[i, j] > thresh else "black",
                           fontweight='bold')
    
    axes[1, 0].set_ylabel('True Label')
    axes[1, 0].set_xlabel('Predicted Label')
    axes[1, 0].set_xticks([0, 1])
    axes[1, 0].set_yticks([0, 1])
    axes[1, 0].set_xticklabels(['Legitimate', 'Scam'])
    axes[1, 0].set_yticklabels(['Legitimate', 'Scam'])
    
    # 4. Feature importance (top SVM coefficients)
    vectorizer = model.named_steps['tfidf']
    classifier = model.named_steps['svm']
    
    feature_names = vectorizer.get_feature_names_out()
    coefficients = classifier.coef_[0]
    
    # Get top 10 positive and negative coefficients
    top_positive_idx = np.argsort(coefficients)[-10:]
    top_negative_idx = np.argsort(coefficients)[:10]
    
    # Combine and create labels
    top_features = []
    top_coefs = []
    colors = []
    
    for idx in top_negative_idx:
        top_features.append(feature_names[idx])
        top_coefs.append(coefficients[idx])
        colors.append('lightcoral')
    
    for idx in reversed(top_positive_idx):
        top_features.append(feature_names[idx])
        top_coefs.append(coefficients[idx])
        colors.append('lightgreen')
    
    y_pos = np.arange(len(top_features))
    axes[1, 1].barh(y_pos, top_coefs, color=colors)
    axes[1, 1].set_yticks(y_pos)
    axes[1, 1].set_yticklabels(top_features, fontsize=8)
    axes[1, 1].set_xlabel('SVM Coefficient')
    axes[1, 1].set_title('Top Feature Weights')
    axes[1, 1].axvline(x=0, color='black', linestyle='-', alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('svm_performance.png', dpi=150, bbox_inches='tight')
    plt.show()
    print("📊 Graph 2: svm_performance.png created")

def create_hyperparameter_graph(df):
    """Create hyperparameter analysis graph"""
    print("\nAnalyzing C parameter effects...")
    
    X = df['cleaned_text']
    y = df['is_scam']
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)
    
    # Test different C values
    C_values = [0.01, 0.1, 1.0, 10.0, 100.0]
    train_scores = []
    test_scores = []
    
    for C in C_values:
        model = Pipeline([
            ('tfidf', TfidfVectorizer(max_features=1500, stop_words='english')),
            ('svm', LinearSVC(C=C, random_state=42, max_iter=2000))
        ])
        
        model.fit(X_train, y_train)
        train_score = model.score(X_train, y_train)
        test_score = model.score(X_test, y_test)
        
        train_scores.append(train_score)
        test_scores.append(test_score)
    
    # Create graph
    plt.figure(figsize=(10, 6))
    plt.semilogx(C_values, train_scores, 'o-', label='Training Accuracy', color='blue', linewidth=2)
    plt.semilogx(C_values, test_scores, 'o-', label='Test Accuracy', color='red', linewidth=2)
    plt.xlabel('C Parameter (Regularization)')
    plt.ylabel('Accuracy')
    plt.title('SVM Hyperparameter Analysis: C Parameter Effect')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Add annotations
    best_C_idx = np.argmax(test_scores)
    best_C = C_values[best_C_idx]
    best_score = test_scores[best_C_idx]
    plt.annotate(f'Best C = {best_C}\nAccuracy = {best_score:.3f}',
                xy=(best_C, best_score), xytext=(best_C*10, best_score-0.05),
                arrowprops=dict(arrowstyle='->', color='red'),
                fontsize=10, ha='center')
    
    plt.tight_layout()
    plt.savefig('svm_hyperparameter.png', dpi=150, bbox_inches='tight')
    plt.show()
    print("📊 Graph 3: svm_hyperparameter.png created")

def main():
    """Main function"""
    # Load data
    df = load_data()
    
    # Create dataset graphs
    create_dataset_graphs(df)
    
    # Train SVM and analyze
    results_df, best_model_data = train_svm_and_analyze(df)
    
    # Create performance graphs
    create_performance_graphs(results_df, best_model_data)
    
    # Create hyperparameter graph
    create_hyperparameter_graph(df)
    
    # Summary
    print("\n" + "="*50)
    print("SVM ANALYSIS COMPLETE")
    print("="*50)
    
    print("\nModel Performance:")
    results_sorted = results_df.sort_values('CV_Mean', ascending=False)
    for _, row in results_sorted.iterrows():
        print(f"  {row['Model']:<15}: {row['CV_Mean']:.4f} CV accuracy")
    
    best_model = results_sorted.iloc[0]
    print(f"\n🏆 Best SVM: {best_model['Model']}")
    print(f"   CV Accuracy: {best_model['CV_Mean']:.4f}")
    print(f"   F1-Score: {best_model['F1_Score']:.4f}")
    
    print(f"\n📊 Graphs Created:")
    print(f"   1. dataset_overview.png - Dataset analysis")
    print(f"   2. svm_performance.png - SVM performance comparison")
    print(f"   3. svm_hyperparameter.png - C parameter analysis")
    
    # Save results
    results_df.to_csv('svm_results.csv', index=False)
    print(f"\n💾 Results saved to: svm_results.csv")

if __name__ == "__main__":
    main()
