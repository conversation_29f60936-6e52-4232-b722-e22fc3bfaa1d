#!/usr/bin/env python3
"""
Visualization Script for Nigerian Scam Detection Dataset
Creates comprehensive graphs and charts showing dataset analysis and model results
"""

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from collections import Counter
import re
from wordcloud import WordCloud
import warnings
warnings.filterwarnings('ignore')

# Set style for better-looking plots
plt.style.use('default')
sns.set_palette("husl")
# Use non-interactive backend
import matplotlib
matplotlib.use('Agg')

class ScamDatasetVisualizer:
    def __init__(self, dataset_path):
        """Initialize the visualizer with dataset"""
        self.dataset_path = dataset_path
        self.df = None
        self.load_data()
        
    def load_data(self):
        """Load and prepare the dataset"""
        print("Loading dataset for visualization...")
        self.df = pd.read_csv(self.dataset_path)
        
        # Create binary classification
        scam_labels = ['ADVANCE_FEE_SCAM', 'ROMANCE_SCAM', 'INVESTMENT_SCAM', 'SCAM_SAMPLE', 'SCAM_ALERT', 'SCAM_WARNING']
        self.df['is_scam'] = self.df['Label'].apply(lambda x: 1 if x in scam_labels else 0)
        
        # Add text statistics
        self.df['text_length'] = self.df['Text Message / Email / Chat'].str.len()
        self.df['word_count'] = self.df['Text Message / Email / Chat'].str.split().str.len()
        
        print(f"Dataset loaded: {len(self.df)} records")
        
    def plot_label_distribution(self):
        """Plot distribution of all labels"""
        plt.figure(figsize=(14, 8))
        
        # Count plot
        plt.subplot(2, 2, 1)
        label_counts = self.df['Label'].value_counts()
        colors = plt.cm.Set3(np.linspace(0, 1, len(label_counts)))
        bars = plt.bar(range(len(label_counts)), label_counts.values, color=colors)
        plt.title('Distribution of All Labels', fontsize=14, fontweight='bold')
        plt.xlabel('Label Categories')
        plt.ylabel('Number of Messages')
        plt.xticks(range(len(label_counts)), label_counts.index, rotation=45, ha='right')
        
        # Add value labels on bars
        for bar, value in zip(bars, label_counts.values):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5, 
                    str(value), ha='center', va='bottom', fontweight='bold')
        
        # Pie chart for binary classification
        plt.subplot(2, 2, 2)
        binary_counts = self.df['is_scam'].value_counts()
        labels = ['Legitimate', 'Scam']
        colors = ['#2ecc71', '#e74c3c']
        explode = (0.05, 0.05)
        
        plt.pie(binary_counts.values, labels=labels, colors=colors, explode=explode,
                autopct='%1.1f%%', startangle=90, shadow=True)
        plt.title('Scam vs Legitimate Distribution', fontsize=14, fontweight='bold')
        
        # Horizontal bar chart for better readability
        plt.subplot(2, 1, 2)
        label_counts_sorted = label_counts.sort_values()
        colors = plt.cm.viridis(np.linspace(0, 1, len(label_counts_sorted)))
        bars = plt.barh(range(len(label_counts_sorted)), label_counts_sorted.values, color=colors)
        plt.title('Detailed Label Distribution (Sorted)', fontsize=14, fontweight='bold')
        plt.xlabel('Number of Messages')
        plt.yticks(range(len(label_counts_sorted)), label_counts_sorted.index)
        
        # Add value labels
        for i, (bar, value) in enumerate(zip(bars, label_counts_sorted.values)):
            plt.text(value + 0.5, i, str(value), va='center', fontweight='bold')
        
        plt.tight_layout()
        plt.savefig('label_distribution.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("   ✓ Label distribution plot saved as 'label_distribution.png'")
        
    def plot_text_statistics(self):
        """Plot text length and word count statistics"""
        plt.figure(figsize=(15, 10))
        
        # Text length distribution
        plt.subplot(2, 3, 1)
        scam_lengths = self.df[self.df['is_scam'] == 1]['text_length']
        legit_lengths = self.df[self.df['is_scam'] == 0]['text_length']
        
        plt.hist(scam_lengths, bins=30, alpha=0.7, label='Scam', color='red', density=True)
        plt.hist(legit_lengths, bins=30, alpha=0.7, label='Legitimate', color='green', density=True)
        plt.title('Text Length Distribution', fontweight='bold')
        plt.xlabel('Characters')
        plt.ylabel('Density')
        plt.legend()
        
        # Word count distribution
        plt.subplot(2, 3, 2)
        scam_words = self.df[self.df['is_scam'] == 1]['word_count']
        legit_words = self.df[self.df['is_scam'] == 0]['word_count']
        
        plt.hist(scam_words, bins=30, alpha=0.7, label='Scam', color='red', density=True)
        plt.hist(legit_words, bins=30, alpha=0.7, label='Legitimate', color='green', density=True)
        plt.title('Word Count Distribution', fontweight='bold')
        plt.xlabel('Words')
        plt.ylabel('Density')
        plt.legend()
        
        # Box plots for comparison
        plt.subplot(2, 3, 3)
        data_to_plot = [scam_lengths, legit_lengths]
        box = plt.boxplot(data_to_plot, labels=['Scam', 'Legitimate'], patch_artist=True)
        box['boxes'][0].set_facecolor('red')
        box['boxes'][1].set_facecolor('green')
        plt.title('Text Length Comparison', fontweight='bold')
        plt.ylabel('Characters')
        
        plt.subplot(2, 3, 4)
        data_to_plot = [scam_words, legit_words]
        box = plt.boxplot(data_to_plot, labels=['Scam', 'Legitimate'], patch_artist=True)
        box['boxes'][0].set_facecolor('red')
        box['boxes'][1].set_facecolor('green')
        plt.title('Word Count Comparison', fontweight='bold')
        plt.ylabel('Words')
        
        # Statistics table
        plt.subplot(2, 3, 5)
        stats_data = {
            'Metric': ['Mean Length', 'Median Length', 'Mean Words', 'Median Words'],
            'Scam': [scam_lengths.mean(), scam_lengths.median(), scam_words.mean(), scam_words.median()],
            'Legitimate': [legit_lengths.mean(), legit_lengths.median(), legit_words.mean(), legit_words.median()]
        }
        
        stats_df = pd.DataFrame(stats_data)
        plt.axis('tight')
        plt.axis('off')
        table = plt.table(cellText=[[f'{val:.1f}' if isinstance(val, float) else val for val in row] 
                                   for row in stats_df.values],
                         colLabels=stats_df.columns,
                         cellLoc='center',
                         loc='center')
        table.auto_set_font_size(False)
        table.set_fontsize(10)
        table.scale(1.2, 1.5)
        plt.title('Text Statistics Summary', fontweight='bold', pad=20)
        
        # Scatter plot
        plt.subplot(2, 3, 6)
        scam_data = self.df[self.df['is_scam'] == 1]
        legit_data = self.df[self.df['is_scam'] == 0]
        
        plt.scatter(scam_data['word_count'], scam_data['text_length'], 
                   alpha=0.6, color='red', label='Scam', s=30)
        plt.scatter(legit_data['word_count'], legit_data['text_length'], 
                   alpha=0.6, color='green', label='Legitimate', s=30)
        plt.title('Word Count vs Text Length', fontweight='bold')
        plt.xlabel('Word Count')
        plt.ylabel('Text Length')
        plt.legend()
        
        plt.tight_layout()
        plt.savefig('text_statistics.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("   ✓ Text statistics plot saved as 'text_statistics.png'")
        
    def plot_word_clouds(self):
        """Create word clouds for scam and legitimate messages"""
        try:
            plt.figure(figsize=(16, 8))
            
            # Prepare text data
            scam_text = ' '.join(self.df[self.df['is_scam'] == 1]['Text Message / Email / Chat'])
            legit_text = ' '.join(self.df[self.df['is_scam'] == 0]['Text Message / Email / Chat'])
            
            # Clean text for word clouds
            def clean_for_wordcloud(text):
                text = text.lower()
                text = re.sub(r'[^\w\s]', ' ', text)
                text = re.sub(r'\d+', '', text)
                return text
            
            scam_text_clean = clean_for_wordcloud(scam_text)
            legit_text_clean = clean_for_wordcloud(legit_text)
            
            # Scam word cloud
            plt.subplot(1, 2, 1)
            wordcloud_scam = WordCloud(width=800, height=400, 
                                     background_color='white',
                                     colormap='Reds',
                                     max_words=100,
                                     stopwords={'you', 'your', 'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}).generate(scam_text_clean)
            
            plt.imshow(wordcloud_scam, interpolation='bilinear')
            plt.title('Most Common Words in SCAM Messages', fontsize=16, fontweight='bold', color='red')
            plt.axis('off')
            
            # Legitimate word cloud
            plt.subplot(1, 2, 2)
            wordcloud_legit = WordCloud(width=800, height=400, 
                                      background_color='white',
                                      colormap='Greens',
                                      max_words=100,
                                      stopwords={'you', 'your', 'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}).generate(legit_text_clean)
            
            plt.imshow(wordcloud_legit, interpolation='bilinear')
            plt.title('Most Common Words in LEGITIMATE Messages', fontsize=16, fontweight='bold', color='green')
            plt.axis('off')
            
            plt.tight_layout()
            plt.savefig('word_clouds.png', dpi=300, bbox_inches='tight')
            plt.close()
            print("   ✓ Word clouds saved as 'word_clouds.png'")
            
        except ImportError:
            print("WordCloud not available. Install with: pip install wordcloud")
            self.plot_top_words()
    
    def plot_top_words(self):
        """Plot top words as bar charts if WordCloud is not available"""
        plt.figure(figsize=(16, 8))
        
        def get_top_words(texts, n=15):
            all_text = ' '.join(texts).lower()
            words = re.findall(r'\b[a-z]+\b', all_text)
            stop_words = {'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'a', 'an', 'this', 'that', 'these', 'those'}
            words = [w for w in words if w not in stop_words and len(w) > 2]
            return Counter(words).most_common(n)
        
        # Get top words
        scam_texts = self.df[self.df['is_scam'] == 1]['Text Message / Email / Chat'].tolist()
        legit_texts = self.df[self.df['is_scam'] == 0]['Text Message / Email / Chat'].tolist()
        
        scam_words = get_top_words(scam_texts)
        legit_words = get_top_words(legit_texts)
        
        # Plot scam words
        plt.subplot(1, 2, 1)
        words, counts = zip(*scam_words)
        bars = plt.barh(range(len(words)), counts, color='red', alpha=0.7)
        plt.title('Top Words in SCAM Messages', fontsize=14, fontweight='bold')
        plt.xlabel('Frequency')
        plt.yticks(range(len(words)), words)
        plt.gca().invert_yaxis()
        
        # Add value labels
        for i, (bar, count) in enumerate(zip(bars, counts)):
            plt.text(count + 0.5, i, str(count), va='center', fontweight='bold')
        
        # Plot legitimate words
        plt.subplot(1, 2, 2)
        words, counts = zip(*legit_words)
        bars = plt.barh(range(len(words)), counts, color='green', alpha=0.7)
        plt.title('Top Words in LEGITIMATE Messages', fontsize=14, fontweight='bold')
        plt.xlabel('Frequency')
        plt.yticks(range(len(words)), words)
        plt.gca().invert_yaxis()
        
        # Add value labels
        for i, (bar, count) in enumerate(zip(bars, counts)):
            plt.text(count + 0.5, i, str(count), va='center', fontweight='bold')
        
        plt.tight_layout()
        plt.savefig('top_words.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("   ✓ Top words plot saved as 'top_words.png'")
    
    def plot_model_performance(self):
        """Plot model performance metrics"""
        # Sample model results (you can replace with actual results)
        models = ['Naive Bayes', 'Logistic Regression', 'Random Forest', 'SVM']
        test_accuracy = [0.8750, 0.8333, 0.8750, 0.8750]
        cv_accuracy = [0.9318, 0.9215, 0.8897, 0.9476]
        
        plt.figure(figsize=(14, 10))
        
        # Model comparison
        plt.subplot(2, 2, 1)
        x = np.arange(len(models))
        width = 0.35
        
        bars1 = plt.bar(x - width/2, test_accuracy, width, label='Test Accuracy', color='skyblue', alpha=0.8)
        bars2 = plt.bar(x + width/2, cv_accuracy, width, label='CV Accuracy', color='lightcoral', alpha=0.8)
        
        plt.title('Model Performance Comparison', fontweight='bold', fontsize=14)
        plt.xlabel('Models')
        plt.ylabel('Accuracy')
        plt.xticks(x, models, rotation=45)
        plt.legend()
        plt.ylim(0.8, 1.0)
        
        # Add value labels
        for bars in [bars1, bars2]:
            for bar in bars:
                height = bar.get_height()
                plt.text(bar.get_x() + bar.get_width()/2., height + 0.005,
                        f'{height:.3f}', ha='center', va='bottom', fontweight='bold')
        
        # Confusion Matrix (for best model)
        plt.subplot(2, 2, 2)
        cm = np.array([[19, 3], [3, 23]])
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                   xticklabels=['Legitimate', 'Scam'],
                   yticklabels=['Legitimate', 'Scam'])
        plt.title('Confusion Matrix (Best Model)', fontweight='bold')
        plt.xlabel('Predicted')
        plt.ylabel('Actual')
        
        # Precision, Recall, F1-Score
        plt.subplot(2, 2, 3)
        metrics = ['Precision', 'Recall', 'F1-Score']
        legitimate_scores = [0.86, 0.86, 0.86]
        scam_scores = [0.88, 0.88, 0.88]
        
        x = np.arange(len(metrics))
        width = 0.35
        
        plt.bar(x - width/2, legitimate_scores, width, label='Legitimate', color='green', alpha=0.7)
        plt.bar(x + width/2, scam_scores, width, label='Scam', color='red', alpha=0.7)
        
        plt.title('Detailed Performance Metrics', fontweight='bold')
        plt.xlabel('Metrics')
        plt.ylabel('Score')
        plt.xticks(x, metrics)
        plt.legend()
        plt.ylim(0.8, 0.9)
        
        # ROC-like visualization (simplified)
        plt.subplot(2, 2, 4)
        accuracy_progression = [0.85, 0.87, 0.89, 0.88, 0.875]
        epochs = range(1, len(accuracy_progression) + 1)
        
        plt.plot(epochs, accuracy_progression, 'bo-', linewidth=2, markersize=8)
        plt.title('Model Training Progress', fontweight='bold')
        plt.xlabel('Training Iteration')
        plt.ylabel('Accuracy')
        plt.grid(True, alpha=0.3)
        plt.ylim(0.84, 0.90)
        
        plt.tight_layout()
        plt.savefig('model_performance.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("   ✓ Model performance plot saved as 'model_performance.png'")
    
    def create_comprehensive_dashboard(self):
        """Create a comprehensive dashboard with all visualizations"""
        print("\n" + "="*60)
        print("CREATING COMPREHENSIVE VISUALIZATION DASHBOARD")
        print("="*60)
        
        print("1. Creating label distribution plots...")
        self.plot_label_distribution()
        
        print("2. Creating text statistics plots...")
        self.plot_text_statistics()
        
        print("3. Creating word frequency visualizations...")
        self.plot_word_clouds()
        
        print("4. Creating model performance plots...")
        self.plot_model_performance()
        
        print("\n" + "="*60)
        print("VISUALIZATION DASHBOARD COMPLETE!")
        print("="*60)
        print("Generated files:")
        print("- label_distribution.png")
        print("- text_statistics.png") 
        print("- word_clouds.png (or top_words.png)")
        print("- model_performance.png")

def main():
    """Main function to create all visualizations"""
    # Find the latest dataset
    import glob
    import os
    
    dataset_files = glob.glob("nigerian_scam_dataset_*.csv")
    if not dataset_files:
        dataset_files = glob.glob("scam_dataset_*.csv")
    
    if not dataset_files:
        print("No dataset file found! Please run the scraper first.")
        return
    
    latest_dataset = max(dataset_files, key=os.path.getctime)
    print(f"Using dataset: {latest_dataset}")
    
    # Create visualizer and generate all plots
    visualizer = ScamDatasetVisualizer(latest_dataset)
    visualizer.create_comprehensive_dashboard()

if __name__ == "__main__":
    main()
