#!/usr/bin/env python3
"""
Comprehensive Naive Bayesian Model Performance Evaluation
Focus on Precision, Recall, and F1-score for Fraud Detection
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split, cross_val_score, StratifiedKFold
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.naive_bayes import MultinomialNB
from sklearn.metrics import (classification_report, confusion_matrix, precision_score, 
                           recall_score, f1_score, accuracy_score, roc_auc_score,
                           precision_recall_curve, roc_curve, auc)
from sklearn.pipeline import Pipeline
import re
from enhanced_dataset_generator import EnhancedDatasetGenerator
import warnings
warnings.filterwarnings('ignore')

# Use non-interactive backend
import matplotlib
matplotlib.use('Agg')

class NaiveBayesPerformanceEvaluator:
    def __init__(self):
        """Initialize the performance evaluator"""
        self.generator = EnhancedDatasetGenerator()
        self.df = None
        self.model = None
        self.X_train = None
        self.X_test = None
        self.y_train = None
        self.y_test = None
        self.y_pred = None
        self.y_pred_proba = None
        
    def generate_evaluation_dataset(self):
        """Generate a larger dataset for comprehensive evaluation"""
        print("Generating comprehensive dataset for performance evaluation...")
        
        # Generate larger dataset for more robust evaluation
        self.generator.generate_advance_fee_scams(50)
        self.generator.generate_romance_scams(40)
        self.generator.generate_investment_scams(45)
        self.generator.generate_legitimate_messages(65)
        
        # Convert to DataFrame
        self.df = pd.DataFrame(self.generator.data)
        
        # Create binary classification (1 = Fraud/Scam, 0 = Legitimate)
        fraud_labels = ['ADVANCE_FEE_SCAM', 'ROMANCE_SCAM', 'INVESTMENT_SCAM']
        self.df['is_fraud'] = self.df['Label'].apply(lambda x: 1 if x in fraud_labels else 0)
        
        # Text preprocessing
        def clean_text(text):
            if pd.isna(text):
                return ""
            text = text.lower()
            text = re.sub(r'http\S+|www\S+|https\S+', '', text, flags=re.MULTILINE)
            text = re.sub(r'\S+@\S+', '', text)
            text = re.sub(r'[^a-zA-Z0-9\s]', ' ', text)
            text = re.sub(r'\s+', ' ', text).strip()
            return text
        
        self.df['cleaned_text'] = self.df['Text Message / Email / Chat'].apply(clean_text)
        self.df = self.df[self.df['cleaned_text'].str.len() >= 10].reset_index(drop=True)
        
        print(f"Dataset generated: {len(self.df)} records")
        print(f"Fraud cases: {self.df['is_fraud'].sum()} ({self.df['is_fraud'].mean():.1%})")
        print(f"Legitimate cases: {(self.df['is_fraud'] == 0).sum()} ({(1-self.df['is_fraud'].mean()):.1%})")
        
        return self.df
    
    def train_naive_bayes_model(self):
        """Train the Naive Bayes model"""
        print("\nTraining Naive Bayes model...")
        
        # Prepare features
        X = self.df['cleaned_text']
        y = self.df['is_fraud']
        
        # Split data
        self.X_train, self.X_test, self.y_train, self.y_test = train_test_split(
            X, y, test_size=0.25, random_state=42, stratify=y
        )
        
        # Create and train model
        self.model = Pipeline([
            ('tfidf', TfidfVectorizer(max_features=3000, stop_words='english', ngram_range=(1, 2))),
            ('nb', MultinomialNB(alpha=1.0))
        ])
        
        self.model.fit(self.X_train, self.y_train)
        
        # Make predictions
        self.y_pred = self.model.predict(self.X_test)
        self.y_pred_proba = self.model.predict_proba(self.X_test)[:, 1]
        
        print(f"Model trained on {len(self.X_train)} samples")
        print(f"Testing on {len(self.X_test)} samples")
    
    def calculate_performance_metrics(self):
        """Calculate comprehensive performance metrics"""
        print("\n" + "="*70)
        print("NAIVE BAYESIAN MODEL PERFORMANCE EVALUATION")
        print("="*70)
        
        # Basic metrics
        accuracy = accuracy_score(self.y_test, self.y_pred)
        precision = precision_score(self.y_test, self.y_pred)
        recall = recall_score(self.y_test, self.y_pred)
        f1 = f1_score(self.y_test, self.y_pred)
        roc_auc = roc_auc_score(self.y_test, self.y_pred_proba)
        
        # Confusion matrix components
        cm = confusion_matrix(self.y_test, self.y_pred)
        tn, fp, fn, tp = cm.ravel()
        
        # Additional metrics
        specificity = tn / (tn + fp)  # True Negative Rate
        false_positive_rate = fp / (fp + tn)
        false_negative_rate = fn / (fn + tp)
        
        print("📊 CORE PERFORMANCE METRICS:")
        print("-" * 50)
        print(f"Accuracy:           {accuracy:.4f} ({accuracy:.1%})")
        print(f"Precision:          {precision:.4f} ({precision:.1%})")
        print(f"Recall (Sensitivity): {recall:.4f} ({recall:.1%})")
        print(f"F1-Score:           {f1:.4f}")
        print(f"Specificity:        {specificity:.4f} ({specificity:.1%})")
        print(f"ROC-AUC:            {roc_auc:.4f}")
        
        print(f"\n🎯 FRAUD DETECTION CONTEXT:")
        print("-" * 50)
        print(f"True Positives (TP):  {tp} - Correctly identified fraud cases")
        print(f"True Negatives (TN):  {tn} - Correctly identified legitimate cases")
        print(f"False Positives (FP): {fp} - Legitimate cases flagged as fraud")
        print(f"False Negatives (FN): {fn} - Fraud cases missed by model")
        
        print(f"\n💰 BUSINESS IMPACT ANALYSIS:")
        print("-" * 50)
        print(f"False Positive Rate:  {false_positive_rate:.4f} ({false_positive_rate:.1%})")
        print(f"  → Customer inconvenience from legitimate transactions flagged")
        print(f"False Negative Rate:  {false_negative_rate:.4f} ({false_negative_rate:.1%})")
        print(f"  → Financial losses from undetected fraud")
        
        # Detailed interpretation
        self.interpret_metrics(precision, recall, f1, false_positive_rate, false_negative_rate)
        
        return {
            'accuracy': accuracy, 'precision': precision, 'recall': recall, 'f1': f1,
            'specificity': specificity, 'roc_auc': roc_auc, 'tp': tp, 'tn': tn, 'fp': fp, 'fn': fn
        }
    
    def interpret_metrics(self, precision, recall, f1, fpr, fnr):
        """Provide detailed interpretation of metrics"""
        print(f"\n🔍 DETAILED METRIC INTERPRETATION:")
        print("-" * 50)
        
        # Precision interpretation
        print(f"📈 PRECISION ({precision:.1%}):")
        if precision >= 0.95:
            print(f"   ✅ EXCELLENT: Very few legitimate transactions flagged as fraud")
            print(f"   ✅ Minimal customer inconvenience and operational costs")
        elif precision >= 0.85:
            print(f"   🟡 GOOD: Acceptable level of false positives")
            print(f"   🟡 Moderate customer inconvenience")
        else:
            print(f"   🔴 NEEDS IMPROVEMENT: High false positive rate")
            print(f"   🔴 Significant customer inconvenience and operational costs")
        
        # Recall interpretation
        print(f"\n🎯 RECALL/SENSITIVITY ({recall:.1%}):")
        if recall >= 0.95:
            print(f"   ✅ EXCELLENT: Very few fraud cases missed")
            print(f"   ✅ Minimal financial losses from undetected fraud")
        elif recall >= 0.85:
            print(f"   🟡 GOOD: Acceptable level of fraud detection")
            print(f"   🟡 Some financial risk from missed fraud")
        else:
            print(f"   🔴 CRITICAL: High number of fraud cases missed")
            print(f"   🔴 Significant financial losses from undetected fraud")
        
        # F1-Score interpretation
        print(f"\n⚖️ F1-SCORE ({f1:.4f}):")
        if f1 >= 0.95:
            print(f"   ✅ EXCELLENT: Optimal balance between precision and recall")
        elif f1 >= 0.85:
            print(f"   🟡 GOOD: Reasonable balance with room for improvement")
        else:
            print(f"   🔴 NEEDS IMPROVEMENT: Poor balance between precision and recall")
        
        # Business recommendations
        print(f"\n💼 BUSINESS RECOMMENDATIONS:")
        print("-" * 50)
        if precision < 0.85:
            print(f"   📋 Consider increasing precision threshold to reduce false positives")
        if recall < 0.85:
            print(f"   📋 Consider lowering threshold or adding features to catch more fraud")
        if f1 >= 0.90:
            print(f"   🎉 Model is production-ready for fraud detection")
        else:
            print(f"   ⚠️ Model needs optimization before production deployment")
    
    def cross_validation_analysis(self):
        """Perform cross-validation analysis"""
        print(f"\n📊 CROSS-VALIDATION ANALYSIS:")
        print("-" * 50)
        
        # Stratified K-Fold cross-validation
        skf = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
        
        # Calculate CV scores for different metrics
        cv_accuracy = cross_val_score(self.model, self.X_train, self.y_train, cv=skf, scoring='accuracy')
        cv_precision = cross_val_score(self.model, self.X_train, self.y_train, cv=skf, scoring='precision')
        cv_recall = cross_val_score(self.model, self.X_train, self.y_train, cv=skf, scoring='recall')
        cv_f1 = cross_val_score(self.model, self.X_train, self.y_train, cv=skf, scoring='f1')
        
        print(f"Accuracy:  {cv_accuracy.mean():.4f} ± {cv_accuracy.std():.4f}")
        print(f"Precision: {cv_precision.mean():.4f} ± {cv_precision.std():.4f}")
        print(f"Recall:    {cv_recall.mean():.4f} ± {cv_recall.std():.4f}")
        print(f"F1-Score:  {cv_f1.mean():.4f} ± {cv_f1.std():.4f}")
        
        # Stability assessment
        if cv_precision.std() < 0.05 and cv_recall.std() < 0.05:
            print(f"✅ Model shows stable performance across folds")
        else:
            print(f"⚠️ Model performance varies across folds - consider more data")
        
        return cv_accuracy, cv_precision, cv_recall, cv_f1
    
    def create_performance_visualizations(self):
        """Create comprehensive performance visualizations"""
        print(f"\nCreating performance visualizations...")
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        
        # 1. Confusion Matrix
        cm = confusion_matrix(self.y_test, self.y_pred)
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', ax=axes[0,0],
                   xticklabels=['Legitimate', 'Fraud'], yticklabels=['Legitimate', 'Fraud'])
        axes[0,0].set_title('Confusion Matrix', fontweight='bold')
        axes[0,0].set_xlabel('Predicted')
        axes[0,0].set_ylabel('Actual')
        
        # 2. Precision-Recall Curve
        precision_curve, recall_curve, _ = precision_recall_curve(self.y_test, self.y_pred_proba)
        pr_auc = auc(recall_curve, precision_curve)
        axes[0,1].plot(recall_curve, precision_curve, color='blue', lw=2, 
                      label=f'PR Curve (AUC = {pr_auc:.3f})')
        axes[0,1].set_xlabel('Recall')
        axes[0,1].set_ylabel('Precision')
        axes[0,1].set_title('Precision-Recall Curve', fontweight='bold')
        axes[0,1].legend()
        axes[0,1].grid(True, alpha=0.3)
        
        # 3. ROC Curve
        fpr, tpr, _ = roc_curve(self.y_test, self.y_pred_proba)
        roc_auc = auc(fpr, tpr)
        axes[0,2].plot(fpr, tpr, color='darkorange', lw=2, label=f'ROC Curve (AUC = {roc_auc:.3f})')
        axes[0,2].plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--')
        axes[0,2].set_xlabel('False Positive Rate')
        axes[0,2].set_ylabel('True Positive Rate')
        axes[0,2].set_title('ROC Curve', fontweight='bold')
        axes[0,2].legend()
        axes[0,2].grid(True, alpha=0.3)
        
        # 4. Performance Metrics Bar Chart
        metrics = ['Accuracy', 'Precision', 'Recall', 'F1-Score', 'Specificity']
        values = [
            accuracy_score(self.y_test, self.y_pred),
            precision_score(self.y_test, self.y_pred),
            recall_score(self.y_test, self.y_pred),
            f1_score(self.y_test, self.y_pred),
            confusion_matrix(self.y_test, self.y_pred)[0,0] / (confusion_matrix(self.y_test, self.y_pred)[0,0] + confusion_matrix(self.y_test, self.y_pred)[0,1])
        ]
        
        colors = ['skyblue', 'lightgreen', 'lightcoral', 'gold', 'plum']
        bars = axes[1,0].bar(metrics, values, color=colors, alpha=0.8)
        axes[1,0].set_title('Performance Metrics', fontweight='bold')
        axes[1,0].set_ylabel('Score')
        axes[1,0].set_ylim(0, 1)
        
        # Add value labels on bars
        for bar, value in zip(bars, values):
            axes[1,0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                          f'{value:.3f}', ha='center', va='bottom', fontweight='bold')
        
        # 5. Cross-validation results
        cv_accuracy, cv_precision, cv_recall, cv_f1 = self.cross_validation_analysis()
        cv_metrics = ['Accuracy', 'Precision', 'Recall', 'F1-Score']
        cv_means = [cv_accuracy.mean(), cv_precision.mean(), cv_recall.mean(), cv_f1.mean()]
        cv_stds = [cv_accuracy.std(), cv_precision.std(), cv_recall.std(), cv_f1.std()]
        
        axes[1,1].bar(cv_metrics, cv_means, yerr=cv_stds, capsize=5, color=colors[:4], alpha=0.8)
        axes[1,1].set_title('Cross-Validation Results', fontweight='bold')
        axes[1,1].set_ylabel('Score')
        axes[1,1].set_ylim(0, 1)
        
        # 6. Error Analysis
        tn, fp, fn, tp = confusion_matrix(self.y_test, self.y_pred).ravel()
        error_types = ['True\nNegatives', 'False\nPositives', 'False\nNegatives', 'True\nPositives']
        error_counts = [tn, fp, fn, tp]
        error_colors = ['green', 'orange', 'red', 'blue']
        
        bars = axes[1,2].bar(error_types, error_counts, color=error_colors, alpha=0.7)
        axes[1,2].set_title('Prediction Breakdown', fontweight='bold')
        axes[1,2].set_ylabel('Count')
        
        # Add value labels
        for bar, count in zip(bars, error_counts):
            axes[1,2].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                          str(count), ha='center', va='bottom', fontweight='bold')
        
        plt.tight_layout()
        plt.savefig('naive_bayes_performance_evaluation.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("   ✓ Performance evaluation saved as 'naive_bayes_performance_evaluation.png'")
    
    def run_comprehensive_evaluation(self):
        """Run the complete performance evaluation"""
        print("="*80)
        print("COMPREHENSIVE NAIVE BAYESIAN MODEL PERFORMANCE EVALUATION")
        print("="*80)
        
        # Generate dataset and train model
        self.generate_evaluation_dataset()
        self.train_naive_bayes_model()
        
        # Calculate and interpret metrics
        metrics = self.calculate_performance_metrics()
        
        # Create visualizations
        self.create_performance_visualizations()
        
        print(f"\n" + "="*80)
        print("EVALUATION SUMMARY")
        print("="*80)
        print(f"✓ Dataset: {len(self.df)} records ({self.df['is_fraud'].sum()} fraud, {(self.df['is_fraud']==0).sum()} legitimate)")
        print(f"✓ Model: Multinomial Naive Bayes with TF-IDF")
        print(f"✓ Test Set: {len(self.y_test)} samples")
        print(f"✓ Precision: {metrics['precision']:.1%} (fraud prediction accuracy)")
        print(f"✓ Recall: {metrics['recall']:.1%} (fraud detection rate)")
        print(f"✓ F1-Score: {metrics['f1']:.4f} (balanced performance)")
        print(f"✓ Visualization: naive_bayes_performance_evaluation.png")
        
        if metrics['precision'] >= 0.9 and metrics['recall'] >= 0.9:
            print(f"\n🎉 EXCELLENT: Model ready for production fraud detection!")
        elif metrics['f1'] >= 0.85:
            print(f"\n✅ GOOD: Model suitable for fraud detection with monitoring")
        else:
            print(f"\n⚠️ NEEDS IMPROVEMENT: Consider model optimization")

def main():
    """Main function to run performance evaluation"""
    evaluator = NaiveBayesPerformanceEvaluator()
    evaluator.run_comprehensive_evaluation()

if __name__ == "__main__":
    main()
