#!/usr/bin/env python3
"""
Test script for the scam dataset scraper
"""

import sys
import os
from scam_dataset_scraper import ScamDatasetScraper
import pandas as pd

def test_basic_functionality():
    """Test basic scraper functionality"""
    print("Testing ScamDatasetScraper...")
    
    scraper = ScamDatasetScraper()
    
    # Test adding sample data
    scraper.add_sample_data()
    
    print(f"Sample data added: {len(scraper.data)} records")
    
    # Test data structure
    if scraper.data:
        sample_record = scraper.data[0]
        required_fields = ['ID', 'Text Message / Email / Chat', 'Label']
        
        for field in required_fields:
            if field not in sample_record:
                print(f"ERROR: Missing required field '{field}'")
                return False
        
        print("✓ Data structure is correct")
    
    # Test saving functionality
    test_filename = "test_dataset.csv"
    scraper.save_dataset(test_filename)
    
    if os.path.exists(test_filename):
        print("✓ CSV file saved successfully")
        
        # Verify CSV content
        df = pd.read_csv(test_filename)
        print(f"✓ CSV contains {len(df)} records")
        print(f"✓ Columns: {list(df.columns)}")
        
        # Clean up test file
        os.remove(test_filename)
        json_file = test_filename.replace('.csv', '.json')
        if os.path.exists(json_file):
            os.remove(json_file)
        
        print("✓ Test files cleaned up")
    else:
        print("ERROR: CSV file was not created")
        return False
    
    print("✓ All basic tests passed!")
    return True

def test_single_website():
    """Test scraping a single website"""
    print("\nTesting single website scraping...")
    
    scraper = ScamDatasetScraper()
    
    # Test CBN website (should be accessible)
    try:
        scraper.scrape_with_requests(
            'https://www.cbn.gov.ng/rates/FinancialData.html', 
            'TEST_LEGITIMATE'
        )
        
        if scraper.data:
            print(f"✓ Successfully scraped CBN website: {len(scraper.data)} records")
            print(f"Sample text: {scraper.data[0]['Text Message / Email / Chat'][:100]}...")
        else:
            print("⚠ No data extracted from CBN website (may be due to site structure)")
            
    except Exception as e:
        print(f"⚠ Error testing CBN website: {e}")
    
    return True

if __name__ == "__main__":
    print("Running Scam Dataset Scraper Tests")
    print("=" * 40)
    
    # Run tests
    basic_test = test_basic_functionality()
    web_test = test_single_website()
    
    print("\n" + "=" * 40)
    if basic_test and web_test:
        print("✓ All tests completed successfully!")
        print("\nYou can now run the main scraper with:")
        print("python scam_dataset_scraper.py")
    else:
        print("⚠ Some tests failed. Check the output above.")
        sys.exit(1)
