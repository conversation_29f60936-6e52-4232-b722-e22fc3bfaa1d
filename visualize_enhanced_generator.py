#!/usr/bin/env python3
"""
Visualization Script for Enhanced Dataset Generator
Creates graphs and charts showing the patterns generated by enhanced_dataset_generator.py
"""

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from collections import Counter
import re
from enhanced_dataset_generator import EnhancedDatasetGenerator
import warnings
warnings.filterwarnings('ignore')

# Use non-interactive backend for saving plots
import matplotlib
matplotlib.use('Agg')

# Set style
plt.style.use('default')
sns.set_palette("husl")

class EnhancedGeneratorVisualizer:
    def __init__(self):
        """Initialize the visualizer"""
        self.generator = EnhancedDatasetGenerator()
        self.df = None
        
    def generate_and_load_data(self):
        """Generate data using the enhanced generator and load it"""
        print("Generating data using EnhancedDatasetGenerator...")
        
        # Generate all types of data
        self.generator.generate_advance_fee_scams(30)
        self.generator.generate_romance_scams(20)
        self.generator.generate_investment_scams(25)
        self.generator.generate_legitimate_messages(35)
        
        # Convert to DataFrame
        self.df = pd.DataFrame(self.generator.data)
        
        # Add text statistics
        self.df['text_length'] = self.df['Text Message / Email / Chat'].str.len()
        self.df['word_count'] = self.df['Text Message / Email / Chat'].str.split().str.len()
        
        # Create binary classification
        scam_labels = ['ADVANCE_FEE_SCAM', 'ROMANCE_SCAM', 'INVESTMENT_SCAM']
        self.df['is_scam'] = self.df['Label'].apply(lambda x: 1 if x in scam_labels else 0)
        
        print(f"Generated {len(self.df)} records")
        return self.df
    
    def plot_generation_overview(self):
        """Plot overview of generated data"""
        plt.figure(figsize=(16, 10))
        
        # Label distribution pie chart
        plt.subplot(2, 3, 1)
        label_counts = self.df['Label'].value_counts()
        colors = ['#ff9999', '#66b3ff', '#99ff99', '#ffcc99']
        explode = (0.05, 0.05, 0.05, 0.05)
        
        plt.pie(label_counts.values, labels=label_counts.index, colors=colors, 
                explode=explode, autopct='%1.1f%%', startangle=90, shadow=True)
        plt.title('Generated Data Distribution', fontsize=14, fontweight='bold')
        
        # Bar chart with counts
        plt.subplot(2, 3, 2)
        bars = plt.bar(label_counts.index, label_counts.values, 
                      color=['#e74c3c', '#3498db', '#2ecc71', '#f39c12'])
        plt.title('Message Count by Category', fontweight='bold')
        plt.xlabel('Category')
        plt.ylabel('Number of Messages')
        plt.xticks(rotation=45, ha='right')
        
        # Add value labels on bars
        for bar, value in zip(bars, label_counts.values):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5, 
                    str(value), ha='center', va='bottom', fontweight='bold')
        
        # Binary classification
        plt.subplot(2, 3, 3)
        binary_counts = self.df['is_scam'].value_counts()
        labels = ['Legitimate', 'Scam']
        colors = ['#2ecc71', '#e74c3c']
        
        plt.pie(binary_counts.values, labels=labels, colors=colors, 
                autopct='%1.1f%%', startangle=90, shadow=True)
        plt.title('Scam vs Legitimate', fontweight='bold')
        
        # Text length distribution
        plt.subplot(2, 3, 4)
        scam_lengths = self.df[self.df['is_scam'] == 1]['text_length']
        legit_lengths = self.df[self.df['is_scam'] == 0]['text_length']
        
        plt.hist(scam_lengths, bins=20, alpha=0.7, label='Scam', color='red', density=True)
        plt.hist(legit_lengths, bins=20, alpha=0.7, label='Legitimate', color='green', density=True)
        plt.title('Text Length Distribution', fontweight='bold')
        plt.xlabel('Characters')
        plt.ylabel('Density')
        plt.legend()
        
        # Word count comparison
        plt.subplot(2, 3, 5)
        scam_words = self.df[self.df['is_scam'] == 1]['word_count']
        legit_words = self.df[self.df['is_scam'] == 0]['word_count']
        
        data_to_plot = [scam_words, legit_words]
        box = plt.boxplot(data_to_plot, labels=['Scam', 'Legitimate'], patch_artist=True)
        box['boxes'][0].set_facecolor('red')
        box['boxes'][1].set_facecolor('green')
        plt.title('Word Count Comparison', fontweight='bold')
        plt.ylabel('Words')
        
        # Statistics summary
        plt.subplot(2, 3, 6)
        stats_data = {
            'Category': ['Advance Fee', 'Romance', 'Investment', 'Legitimate'],
            'Count': [
                len(self.df[self.df['Label'] == 'ADVANCE_FEE_SCAM']),
                len(self.df[self.df['Label'] == 'ROMANCE_SCAM']),
                len(self.df[self.df['Label'] == 'INVESTMENT_SCAM']),
                len(self.df[self.df['Label'] == 'LEGITIMATE_FINANCIAL'])
            ],
            'Avg Length': [
                self.df[self.df['Label'] == 'ADVANCE_FEE_SCAM']['text_length'].mean(),
                self.df[self.df['Label'] == 'ROMANCE_SCAM']['text_length'].mean(),
                self.df[self.df['Label'] == 'INVESTMENT_SCAM']['text_length'].mean(),
                self.df[self.df['Label'] == 'LEGITIMATE_FINANCIAL']['text_length'].mean()
            ]
        }
        
        stats_df = pd.DataFrame(stats_data)
        plt.axis('tight')
        plt.axis('off')
        table = plt.table(cellText=[[row[0], row[1], f'{row[2]:.0f}'] for row in stats_df.values],
                         colLabels=stats_df.columns,
                         cellLoc='center',
                         loc='center')
        table.auto_set_font_size(False)
        table.set_fontsize(10)
        table.scale(1.2, 1.5)
        plt.title('Generation Statistics', fontweight='bold', pad=20)
        
        plt.tight_layout()
        plt.savefig('enhanced_generator_overview.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("   ✓ Generator overview saved as 'enhanced_generator_overview.png'")
    
    def plot_scam_patterns(self):
        """Plot patterns in different scam types"""
        plt.figure(figsize=(18, 12))
        
        # Advance Fee Scam patterns
        plt.subplot(2, 3, 1)
        advance_fee_data = self.df[self.df['Label'] == 'ADVANCE_FEE_SCAM']
        
        # Extract amounts mentioned
        amounts = []
        for text in advance_fee_data['Text Message / Email / Chat']:
            amount_matches = re.findall(r'\$(\d+(?:,\d+)*)', text)
            if amount_matches:
                amounts.extend([int(amt.replace(',', '')) for amt in amount_matches])
        
        if amounts:
            plt.hist(amounts, bins=10, color='red', alpha=0.7, edgecolor='black')
            plt.title('Advance Fee Scam - Dollar Amounts', fontweight='bold')
            plt.xlabel('Amount ($)')
            plt.ylabel('Frequency')
            plt.ticklabel_format(style='plain', axis='x')
        
        # Romance Scam patterns
        plt.subplot(2, 3, 2)
        romance_data = self.df[self.df['Label'] == 'ROMANCE_SCAM']
        
        # Extract common romance keywords
        romance_keywords = ['love', 'marry', 'darling', 'honey', 'sweetheart', 'beloved']
        keyword_counts = {}
        
        for keyword in romance_keywords:
            count = sum(1 for text in romance_data['Text Message / Email / Chat'] 
                       if keyword.lower() in text.lower())
            keyword_counts[keyword] = count
        
        if keyword_counts:
            plt.bar(keyword_counts.keys(), keyword_counts.values(), color='pink', alpha=0.8)
            plt.title('Romance Scam - Emotional Keywords', fontweight='bold')
            plt.xlabel('Keywords')
            plt.ylabel('Frequency')
            plt.xticks(rotation=45)
        
        # Investment Scam patterns
        plt.subplot(2, 3, 3)
        investment_data = self.df[self.df['Label'] == 'INVESTMENT_SCAM']
        
        # Extract percentage returns
        returns = []
        for text in investment_data['Text Message / Email / Chat']:
            return_matches = re.findall(r'(\d+)%', text)
            if return_matches:
                returns.extend([int(ret) for ret in return_matches])
        
        if returns:
            plt.hist(returns, bins=8, color='orange', alpha=0.7, edgecolor='black')
            plt.title('Investment Scam - Promised Returns (%)', fontweight='bold')
            plt.xlabel('Return Percentage')
            plt.ylabel('Frequency')
        
        # Nigerian locations mentioned
        plt.subplot(2, 3, 4)
        all_scam_text = ' '.join(self.df[self.df['is_scam'] == 1]['Text Message / Email / Chat'])
        locations = ['Lagos', 'Abuja', 'Kano', 'Ibadan', 'Port Harcourt', 'Benin City', 'Kaduna', 'Jos']
        location_counts = {}
        
        for location in locations:
            count = all_scam_text.lower().count(location.lower())
            if count > 0:
                location_counts[location] = count
        
        if location_counts:
            plt.bar(location_counts.keys(), location_counts.values(), color='blue', alpha=0.7)
            plt.title('Nigerian Locations in Scams', fontweight='bold')
            plt.xlabel('Cities')
            plt.ylabel('Mentions')
            plt.xticks(rotation=45)
        
        # Nigerian banks mentioned
        plt.subplot(2, 3, 5)
        banks = ['First Bank', 'GTBank', 'Access Bank', 'Zenith Bank', 'UBA', 'Fidelity Bank']
        bank_counts = {}
        
        for bank in banks:
            count = all_scam_text.lower().count(bank.lower())
            if count > 0:
                bank_counts[bank] = count
        
        if bank_counts:
            plt.bar(bank_counts.keys(), bank_counts.values(), color='green', alpha=0.7)
            plt.title('Nigerian Banks in Scams', fontweight='bold')
            plt.xlabel('Banks')
            plt.ylabel('Mentions')
            plt.xticks(rotation=45, ha='right')
        
        # Currency patterns
        plt.subplot(2, 3, 6)
        currency_patterns = {
            'USD ($)': len(re.findall(r'\$\d+', all_scam_text)),
            'Naira (₦)': len(re.findall(r'₦\d+', all_scam_text)),
            'Million': all_scam_text.lower().count('million'),
            'Thousand': all_scam_text.lower().count('thousand')
        }
        
        plt.bar(currency_patterns.keys(), currency_patterns.values(), 
               color=['green', 'blue', 'red', 'orange'], alpha=0.7)
        plt.title('Currency Mentions in Scams', fontweight='bold')
        plt.xlabel('Currency/Amount Type')
        plt.ylabel('Frequency')
        plt.xticks(rotation=45)
        
        plt.tight_layout()
        plt.savefig('scam_patterns_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("   ✓ Scam patterns analysis saved as 'scam_patterns_analysis.png'")
    
    def plot_legitimate_patterns(self):
        """Plot patterns in legitimate messages"""
        plt.figure(figsize=(15, 10))
        
        legit_data = self.df[self.df['Label'] == 'LEGITIMATE_FINANCIAL']
        legit_text = ' '.join(legit_data['Text Message / Email / Chat'])
        
        # Government agencies mentioned
        plt.subplot(2, 3, 1)
        agencies = ['CBN', 'EFCC', 'ICPC', 'SEC', 'NDIC', 'PENCOM', 'FCCPC']
        agency_counts = {}
        
        for agency in agencies:
            count = legit_text.upper().count(agency.upper())
            if count > 0:
                agency_counts[agency] = count
        
        if agency_counts:
            plt.bar(agency_counts.keys(), agency_counts.values(), color='blue', alpha=0.7)
            plt.title('Government Agencies in Legitimate Messages', fontweight='bold')
            plt.xlabel('Agencies')
            plt.ylabel('Mentions')
        
        # Financial terms
        plt.subplot(2, 3, 2)
        financial_terms = ['rate', 'bank', 'investment', 'loan', 'interest', 'policy', 'regulation']
        term_counts = {}
        
        for term in financial_terms:
            count = legit_text.lower().count(term.lower())
            if count > 0:
                term_counts[term] = count
        
        if term_counts:
            plt.bar(term_counts.keys(), term_counts.values(), color='green', alpha=0.7)
            plt.title('Financial Terms in Legitimate Messages', fontweight='bold')
            plt.xlabel('Terms')
            plt.ylabel('Frequency')
            plt.xticks(rotation=45)
        
        # Message length comparison by type
        plt.subplot(2, 3, 3)
        categories = self.df['Label'].unique()
        lengths_by_category = [self.df[self.df['Label'] == cat]['text_length'].values for cat in categories]
        
        plt.boxplot(lengths_by_category, labels=categories)
        plt.title('Message Length by Category', fontweight='bold')
        plt.ylabel('Characters')
        plt.xticks(rotation=45, ha='right')
        
        # Word frequency in legitimate vs scam
        plt.subplot(2, 3, 4)
        def get_top_words(texts, n=10):
            all_text = ' '.join(texts).lower()
            words = re.findall(r'\b[a-z]+\b', all_text)
            stop_words = {'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'a', 'an', 'this', 'that', 'these', 'those'}
            words = [w for w in words if w not in stop_words and len(w) > 2]
            return Counter(words).most_common(n)
        
        legit_words = get_top_words(legit_data['Text Message / Email / Chat'].tolist(), 8)
        if legit_words:
            words, counts = zip(*legit_words)
            plt.barh(range(len(words)), counts, color='green', alpha=0.7)
            plt.title('Top Words in Legitimate Messages', fontweight='bold')
            plt.xlabel('Frequency')
            plt.yticks(range(len(words)), words)
            plt.gca().invert_yaxis()
        
        # Scam words
        plt.subplot(2, 3, 5)
        scam_texts = self.df[self.df['is_scam'] == 1]['Text Message / Email / Chat'].tolist()
        scam_words = get_top_words(scam_texts, 8)
        if scam_words:
            words, counts = zip(*scam_words)
            plt.barh(range(len(words)), counts, color='red', alpha=0.7)
            plt.title('Top Words in Scam Messages', fontweight='bold')
            plt.xlabel('Frequency')
            plt.yticks(range(len(words)), words)
            plt.gca().invert_yaxis()
        
        # Generation summary
        plt.subplot(2, 3, 6)
        summary_data = {
            'Total Messages': len(self.df),
            'Scam Messages': len(self.df[self.df['is_scam'] == 1]),
            'Legitimate Messages': len(self.df[self.df['is_scam'] == 0]),
            'Avg Scam Length': self.df[self.df['is_scam'] == 1]['text_length'].mean(),
            'Avg Legit Length': self.df[self.df['is_scam'] == 0]['text_length'].mean()
        }
        
        plt.axis('tight')
        plt.axis('off')
        table_data = [[k, f'{v:.0f}' if isinstance(v, float) else str(v)] for k, v in summary_data.items()]
        table = plt.table(cellText=table_data,
                         colLabels=['Metric', 'Value'],
                         cellLoc='center',
                         loc='center')
        table.auto_set_font_size(False)
        table.set_fontsize(12)
        table.scale(1.2, 1.5)
        plt.title('Enhanced Generator Summary', fontweight='bold', pad=20)
        
        plt.tight_layout()
        plt.savefig('legitimate_patterns_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("   ✓ Legitimate patterns analysis saved as 'legitimate_patterns_analysis.png'")
    
    def create_comprehensive_visualization(self):
        """Create all visualizations for the enhanced generator"""
        print("\n" + "="*60)
        print("ENHANCED DATASET GENERATOR VISUALIZATION")
        print("="*60)
        
        # Generate data
        self.generate_and_load_data()
        
        print("\n1. Creating generation overview...")
        self.plot_generation_overview()
        
        print("2. Creating scam patterns analysis...")
        self.plot_scam_patterns()
        
        print("3. Creating legitimate patterns analysis...")
        self.plot_legitimate_patterns()
        
        print("\n" + "="*60)
        print("ENHANCED GENERATOR VISUALIZATION COMPLETE!")
        print("="*60)
        print("Generated files:")
        print("- enhanced_generator_overview.png")
        print("- scam_patterns_analysis.png")
        print("- legitimate_patterns_analysis.png")
        print(f"\nTotal records generated and analyzed: {len(self.df)}")

def main():
    """Main function to create enhanced generator visualizations"""
    visualizer = EnhancedGeneratorVisualizer()
    visualizer.create_comprehensive_visualization()

if __name__ == "__main__":
    main()
